import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'awesome_notification_service.dart';
import '../models/course_video.dart';
import '../models/course.dart';

/// Comprehensive workout video notification service
/// Handles new video releases, unlock notifications, and smart workout suggestions
class WorkoutVideoNotificationService {
  static final WorkoutVideoNotificationService _instance = WorkoutVideoNotificationService._internal();
  factory WorkoutVideoNotificationService() => _instance;
  WorkoutVideoNotificationService._internal();

  final EnhancedNotificationService _notificationService = EnhancedNotificationService();

  // Storage keys
  static const String _videoNotificationsEnabledKey = 'workout_video_notifications_enabled';
  static const String _newVideoNotificationsKey = 'new_video_notifications_enabled';
  static const String _unlockNotificationsKey = 'unlock_notifications_enabled';
  static const String _smartSuggestionsKey = 'smart_workout_suggestions_enabled';
  static const String _notifyMeVideosKey = 'notify_me_videos';
  static const String _lastKnownVideosKey = 'last_known_videos';
  static const String _smartWorkoutTimeKey = 'smart_workout_time';
  static const String _smartWorkoutSetupKey = 'smart_workout_setup_completed';

  /// Initialize the service
  Future<void> initialize() async {
    await _notificationService.initialize();
    await _checkForNewVideos();
  }

  // ============================================================================
  // GENERAL VIDEO NOTIFICATION SETTINGS
  // ============================================================================

  /// Check if workout video notifications are enabled
  Future<bool> areVideoNotificationsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_videoNotificationsEnabledKey) ?? true; // Default enabled
  }

  /// Enable/disable all workout video notifications
  Future<void> setVideoNotificationsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_videoNotificationsEnabledKey, enabled);
    print('🔔 Workout video notifications ${enabled ? 'enabled' : 'disabled'}');
  }

  /// Check if new video notifications are enabled
  Future<bool> areNewVideoNotificationsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_newVideoNotificationsKey) ?? true;
  }

  /// Enable/disable new video notifications
  Future<void> setNewVideoNotificationsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_newVideoNotificationsKey, enabled);
  }

  /// Check if unlock notifications are enabled
  Future<bool> areUnlockNotificationsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_unlockNotificationsKey) ?? true;
  }

  /// Enable/disable unlock notifications
  Future<void> setUnlockNotificationsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_unlockNotificationsKey, enabled);
  }

  /// Check if smart workout suggestions are enabled
  Future<bool> areSmartSuggestionsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_smartSuggestionsKey) ?? true;
  }

  /// Enable/disable smart workout suggestions
  Future<void> setSmartSuggestionsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_smartSuggestionsKey, enabled);
  }

  // ============================================================================
  // NEW VIDEO RELEASE NOTIFICATIONS
  // ============================================================================

  /// Check for new videos and send notifications
  Future<void> checkForNewVideos() async {
    if (!await areVideoNotificationsEnabled() || !await areNewVideoNotificationsEnabled()) {
      return;
    }

    await _checkForNewVideos();
  }

  Future<void> _checkForNewVideos() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastKnownVideosJson = prefs.getString(_lastKnownVideosKey);
      
      // For now, we'll implement a basic check
      // In a real implementation, this would compare with API data
      print('🔍 Checking for new workout videos...');
      
      // TODO: Implement actual API comparison logic
      // This is a placeholder for the new video detection system
      
    } catch (e) {
      print('❌ Error checking for new videos: $e');
    }
  }

  /// Send notification for new video release
  Future<void> sendNewVideoNotification({
    required String videoTitle,
    required String description,
    String? courseTitle,
    int? videoId,
  }) async {
    if (!await areVideoNotificationsEnabled() || !await areNewVideoNotificationsEnabled()) {
      return;
    }

    try {
      await _notificationService.showBasicNotification(
        id: 1000 + (videoId ?? 0),
        title: '🎬 New Workout Video Available!',
        body: '$videoTitle${courseTitle != null ? ' in $courseTitle' : ''}',
        channelKey: EnhancedNotificationService.workoutVideosChannelId,
      );

      print('🔔 New video notification sent: $videoTitle');
    } catch (e) {
      print('❌ Error sending new video notification: $e');
    }
  }

  // ============================================================================
  // "NOTIFY ME" FEATURE FOR LOCKED VIDEOS
  // ============================================================================

  /// Add video to "notify me" list
  Future<void> addNotifyMeVideo(int videoId, String videoTitle) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notifyMeVideosJson = prefs.getString(_notifyMeVideosKey) ?? '{}';
      final Map<String, dynamic> notifyMeVideos = jsonDecode(notifyMeVideosJson);
      
      notifyMeVideos[videoId.toString()] = {
        'title': videoTitle,
        'added_date': DateTime.now().toIso8601String(),
      };
      
      await prefs.setString(_notifyMeVideosKey, jsonEncode(notifyMeVideos));
      print('🔔 Added video $videoId to notify me list: $videoTitle');
    } catch (e) {
      print('❌ Error adding notify me video: $e');
    }
  }

  /// Remove video from "notify me" list
  Future<void> removeNotifyMeVideo(int videoId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notifyMeVideosJson = prefs.getString(_notifyMeVideosKey) ?? '{}';
      final Map<String, dynamic> notifyMeVideos = jsonDecode(notifyMeVideosJson);
      
      notifyMeVideos.remove(videoId.toString());
      
      await prefs.setString(_notifyMeVideosKey, jsonEncode(notifyMeVideos));
      print('🔔 Removed video $videoId from notify me list');
    } catch (e) {
      print('❌ Error removing notify me video: $e');
    }
  }

  /// Check if video is in "notify me" list
  Future<bool> isVideoInNotifyMeList(int videoId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notifyMeVideosJson = prefs.getString(_notifyMeVideosKey) ?? '{}';
      final Map<String, dynamic> notifyMeVideos = jsonDecode(notifyMeVideosJson);
      
      return notifyMeVideos.containsKey(videoId.toString());
    } catch (e) {
      print('❌ Error checking notify me video: $e');
      return false;
    }
  }

  /// Get all videos in "notify me" list
  Future<Map<int, String>> getNotifyMeVideos() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notifyMeVideosJson = prefs.getString(_notifyMeVideosKey) ?? '{}';
      final Map<String, dynamic> notifyMeVideos = jsonDecode(notifyMeVideosJson);
      
      final Map<int, String> result = {};
      notifyMeVideos.forEach((key, value) {
        final videoId = int.tryParse(key);
        if (videoId != null && value is Map<String, dynamic>) {
          result[videoId] = value['title'] ?? 'Unknown Video';
        }
      });
      
      return result;
    } catch (e) {
      print('❌ Error getting notify me videos: $e');
      return {};
    }
  }

  /// Send unlock notification for videos in "notify me" list
  Future<void> sendUnlockNotification(int videoId, String videoTitle) async {
    if (!await areVideoNotificationsEnabled() || !await areUnlockNotificationsEnabled()) {
      return;
    }

    // Check if user requested notification for this video
    if (!await isVideoInNotifyMeList(videoId)) {
      return;
    }

    try {
      await _notificationService.showBasicNotification(
        id: 2000 + videoId,
        title: '🔓 Video Unlocked!',
        body: '$videoTitle is now available to watch',
        channelKey: EnhancedNotificationService.workoutVideosChannelId,
      );

      // Remove from notify me list since notification was sent
      await removeNotifyMeVideo(videoId);
      
      print('🔔 Unlock notification sent for video: $videoTitle');
    } catch (e) {
      print('❌ Error sending unlock notification: $e');
    }
  }

  // ============================================================================
  // SMART WORKOUT TIME DETECTION
  // ============================================================================

  /// Check if smart workout setup is completed
  Future<bool> isSmartWorkoutSetupCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_smartWorkoutSetupKey) ?? false;
  }

  /// Mark smart workout setup as completed
  Future<void> markSmartWorkoutSetupCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_smartWorkoutSetupKey, true);
  }

  /// Get smart workout time
  Future<String?> getSmartWorkoutTime() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_smartWorkoutTimeKey);
  }

  /// Set smart workout time
  Future<void> setSmartWorkoutTime(String time) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_smartWorkoutTimeKey, time);
    await markSmartWorkoutSetupCompleted();
    print('🧠 Smart workout time set to: $time');
  }

  // ============================================================================
  // TESTING AND VERIFICATION
  // ============================================================================

  /// Send test workout reminder notification
  Future<void> sendTestWorkoutReminder() async {
    try {
      await _notificationService.showBasicNotification(
        id: 9999,
        title: '💪 Test Workout Reminder',
        body: 'This is a test notification to verify your workout reminders are working!',
        channelKey: EnhancedNotificationService.workoutChannelId,
      );

      print('🔔 Test workout reminder sent successfully');
    } catch (e) {
      print('❌ Error sending test workout reminder: $e');
      rethrow;
    }
  }

  /// Send test video notification to verify the system
  Future<void> sendTestVideoNotification() async {
    try {
      await sendNewVideoNotification(
        videoTitle: 'Test Video: Advanced HIIT Workout',
        description: 'This is a test notification for new video releases',
        courseTitle: 'Test Course',
        videoId: 999,
      );
      print('🔔 Test video notification sent successfully');
    } catch (e) {
      print('❌ Error sending test video notification: $e');
      rethrow;
    }
  }

  /// Send test unlock notification
  Future<void> sendTestUnlockNotification() async {
    try {
      // First add a test video to notify list
      await addNotifyMeVideo(998, 'Test Unlock Video');

      // Then send unlock notification
      await sendUnlockNotification(998, 'Test Unlock Video');

      print('🔔 Test unlock notification sent successfully');
    } catch (e) {
      print('❌ Error sending test unlock notification: $e');
      rethrow;
    }
  }
}
