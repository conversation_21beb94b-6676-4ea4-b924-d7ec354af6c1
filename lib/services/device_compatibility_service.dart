import 'dart:async';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Device compatibility service for maximum Android version and manufacturer support
class DeviceCompatibilityService {
  static final DeviceCompatibilityService _instance = DeviceCompatibilityService._internal();
  factory DeviceCompatibilityService() => _instance;
  DeviceCompatibilityService._internal();

  // Device information
  bool _isInitialized = false;
  late DeviceInfo _deviceInfo;
  late PerformanceProfile _performanceProfile;
  CompatibilityProfile? _compatibilityProfile;

  /// Initialize device compatibility service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('📱 Initializing DeviceCompatibilityService...');

      // Gather device information
      print('📱 Calling _gatherDeviceInfo()...');
      await _gatherDeviceInfo();
      print('📱 _gatherDeviceInfo() completed.');

      // Determine performance profile
      print('📱 Calling _determinePerformanceProfile()...');
      _determinePerformanceProfile();
      print('📱 _determinePerformanceProfile() completed.');

      // Create compatibility profile
      print('📱 Calling _createCompatibilityProfile()...');
      _createCompatibilityProfile();
      print('📱 _createCompatibilityProfile() completed. _compatibilityProfile assigned: ${_compatibilityProfile != null}');

      // Apply device-specific optimizations
      print('📱 Calling _applyDeviceOptimizations()...');
      await _applyDeviceOptimizations();
      print('📱 _applyDeviceOptimizations() completed.');

      _isInitialized = true;
      print('✅ DeviceCompatibilityService initialized');
      print('📱 Device: ${_deviceInfo.brand} ${_deviceInfo.model}');
      print('📱 Android: ${_deviceInfo.androidVersion} (API ${_deviceInfo.sdkInt})');
      print('📱 Performance: ${_performanceProfile.tier}');
    } catch (e) {
      print('❌ DeviceCompatibilityService initialization failed: $e');
      _isInitialized = true; // Continue with defaults
    }
  }

  /// Gather comprehensive device information
  Future<void> _gatherDeviceInfo() async {
    final deviceInfoPlugin = DeviceInfoPlugin();
    
    if (Platform.isAndroid) {
      final androidInfo = await deviceInfoPlugin.androidInfo;
      
      _deviceInfo = DeviceInfo(
        brand: androidInfo.brand.toLowerCase(),
        manufacturer: androidInfo.manufacturer.toLowerCase(),
        model: androidInfo.model,
        device: androidInfo.device,
        product: androidInfo.product,
        androidVersion: androidInfo.version.release,
        sdkInt: androidInfo.version.sdkInt,
        securityPatch: androidInfo.version.securityPatch,
        isPhysicalDevice: androidInfo.isPhysicalDevice,
        systemFeatures: androidInfo.systemFeatures,
        supportedAbis: androidInfo.supportedAbis,
        totalMemory: await _getTotalMemory(),
        availableMemory: await _getAvailableMemory(),
      );
    } else {
      // Fallback for non-Android platforms
      _deviceInfo = DeviceInfo.fallback();
    }
  }

  /// Get total device memory
  Future<int> _getTotalMemory() async {
    try {
      // This would require platform-specific implementation
      // For now, estimate based on device characteristics
      return 4 * 1024 * 1024 * 1024; // 4GB default
    } catch (e) {
      return 2 * 1024 * 1024 * 1024; // 2GB fallback
    }
  }

  /// Get available memory
  Future<int> _getAvailableMemory() async {
    try {
      // This would require platform-specific implementation
      return (_deviceInfo.totalMemory * 0.6).round(); // Estimate 60% available
    } catch (e) {
      return 1 * 1024 * 1024 * 1024; // 1GB fallback
    }
  }

  /// Determine device performance profile
  void _determinePerformanceProfile() {
    PerformanceTier tier;
    Map<String, dynamic> optimizations = {};

    // Determine performance tier based on multiple factors
    if (_deviceInfo.sdkInt >= 30 && _deviceInfo.totalMemory >= 6 * 1024 * 1024 * 1024) {
      tier = PerformanceTier.high;
      optimizations = {
        'enableHighQualityAnimations': true,
        'enableAdvancedFeatures': true,
        'cacheSize': 100 * 1024 * 1024, // 100MB
        'imageQuality': 'high',
        'videoQuality': 'high',
      };
    } else if (_deviceInfo.sdkInt >= 26 && _deviceInfo.totalMemory >= 3 * 1024 * 1024 * 1024) {
      tier = PerformanceTier.medium;
      optimizations = {
        'enableHighQualityAnimations': false,
        'enableAdvancedFeatures': true,
        'cacheSize': 50 * 1024 * 1024, // 50MB
        'imageQuality': 'medium',
        'videoQuality': 'medium',
      };
    } else {
      tier = PerformanceTier.low;
      optimizations = {
        'enableHighQualityAnimations': false,
        'enableAdvancedFeatures': false,
        'cacheSize': 25 * 1024 * 1024, // 25MB
        'imageQuality': 'low',
        'videoQuality': 'low',
      };
    }

    _performanceProfile = PerformanceProfile(
      tier: tier,
      optimizations: optimizations,
    );
  }

  /// Create compatibility profile for device-specific handling
  void _createCompatibilityProfile() {
    final quirks = <DeviceQuirk>[];
    final powerManagement = _determinePowerManagementProfile();
    final notificationSupport = _determineNotificationSupport();

    // Add manufacturer-specific quirks
    switch (_deviceInfo.brand) {
      case 'samsung':
        quirks.addAll([
          DeviceQuirk.samsungBatteryOptimization,
          DeviceQuirk.samsungEdgeGestures,
        ]);
        break;
      case 'oneplus':
      case 'oppo':
      case 'realme':
        quirks.addAll([
          DeviceQuirk.aggressivePowerManagement,
          DeviceQuirk.customNotificationSettings,
          DeviceQuirk.backgroundAppKilling,
        ]);
        break;
      case 'xiaomi':
      case 'redmi':
        quirks.addAll([
          DeviceQuirk.miuiPermissions,
          DeviceQuirk.aggressivePowerManagement,
          DeviceQuirk.customNotificationSettings,
          DeviceQuirk.autostartManagement,
        ]);
        break;
      case 'huawei':
      case 'honor':
        quirks.addAll([
          DeviceQuirk.emuiPowerManagement,
          DeviceQuirk.protectedApps,
          DeviceQuirk.customNotificationSettings,
        ]);
        break;
      case 'vivo':
        quirks.addAll([
          DeviceQuirk.vivoBackgroundRestrictions,
          DeviceQuirk.aggressivePowerManagement,
        ]);
        break;
    }

    // Add Android version-specific quirks
    if (_deviceInfo.sdkInt >= 31) {
      quirks.add(DeviceQuirk.android12ExactAlarms);
    }
    if (_deviceInfo.sdkInt >= 33) {
      quirks.add(DeviceQuirk.android13NotificationPermission);
    }

    _compatibilityProfile = CompatibilityProfile(
      quirks: quirks,
      powerManagement: powerManagement,
      notificationSupport: notificationSupport,
      requiresSpecialHandling: quirks.isNotEmpty,
    );
  }

  /// Determine power management profile
  PowerManagementProfile _determinePowerManagementProfile() {
    bool isAggressive = false;
    List<String> optimizationSteps = [];

    switch (_deviceInfo.brand) {
      case 'oneplus':
      case 'oppo':
      case 'realme':
        isAggressive = true;
        optimizationSteps = [
          'Disable battery optimization for KFT',
          'Enable background activity',
          'Add to auto-launch whitelist',
        ];
        break;
      case 'xiaomi':
      case 'redmi':
        isAggressive = true;
        optimizationSteps = [
          'Enable autostart permission',
          'Disable battery saver restrictions',
          'Enable background activity',
          'Add to MIUI security whitelist',
        ];
        break;
      case 'huawei':
      case 'honor':
        isAggressive = true;
        optimizationSteps = [
          'Add to protected apps',
          'Disable battery optimization',
          'Enable background app refresh',
        ];
        break;
      case 'vivo':
        isAggressive = true;
        optimizationSteps = [
          'Enable background app refresh',
          'Add to autostart manager',
          'Disable power consumption control',
        ];
        break;
      case 'samsung':
        isAggressive = false;
        optimizationSteps = [
          'Set battery usage to unrestricted',
          'Disable adaptive battery for KFT',
        ];
        break;
      default:
        isAggressive = false;
        optimizationSteps = [
          'Disable battery optimization',
          'Allow background activity',
        ];
    }

    return PowerManagementProfile(
      isAggressive: isAggressive,
      optimizationSteps: optimizationSteps,
    );
  }

  /// Determine notification support capabilities
  NotificationSupport _determineNotificationSupport() {
    bool supportsExactAlarms = _deviceInfo.sdkInt >= 19;
    bool requiresPermission = _deviceInfo.sdkInt >= 33;
    bool supportsChannels = _deviceInfo.sdkInt >= 26;
    bool supportsGrouping = _deviceInfo.sdkInt >= 24;

    List<String> limitations = [];

    // Add manufacturer-specific limitations
    if (_compatibilityProfile?.quirks.contains(DeviceQuirk.aggressivePowerManagement) == true) {
      limitations.add('May require manual power management configuration');
    }
    if (_compatibilityProfile?.quirks.contains(DeviceQuirk.customNotificationSettings) == true) {
      limitations.add('Custom notification settings required');
    }

    return NotificationSupport(
      supportsExactAlarms: supportsExactAlarms,
      requiresPermission: requiresPermission,
      supportsChannels: supportsChannels,
      supportsGrouping: supportsGrouping,
      limitations: limitations,
    );
  }

  /// Apply device-specific optimizations
  Future<void> _applyDeviceOptimizations() async {
    try {
      // Store device profile for other services
      await _storeDeviceProfile();

      // Apply performance optimizations
      await _applyPerformanceOptimizations();

      print('✅ Device optimizations applied');
    } catch (e) {
      print('❌ Failed to apply device optimizations: $e');
    }
  }

  /// Store device profile in shared preferences
  Future<void> _storeDeviceProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('device_brand', _deviceInfo.brand);
      await prefs.setString('device_model', _deviceInfo.model);
      await prefs.setInt('device_sdk', _deviceInfo.sdkInt);
      await prefs.setString('performance_tier', _performanceProfile.tier.toString());
      await prefs.setBool('aggressive_power_management', _compatibilityProfile?.powerManagement.isAggressive ?? false);
    } catch (e) {
      print('❌ Failed to store device profile: $e');
    }
  }

  /// Apply performance-based optimizations
  Future<void> _applyPerformanceOptimizations() async {
    // This would integrate with your app's performance settings
    // For example, adjusting animation durations, cache sizes, etc.
    
    if (_performanceProfile.tier == PerformanceTier.low) {
      // Reduce animations and effects for low-end devices
      print('📱 Applied low-end device optimizations');
    }
  }

  /// Get device information
  DeviceInfo get deviceInfo => _deviceInfo;

  /// Get performance profile
  PerformanceProfile get performanceProfile => _performanceProfile;

  /// Get compatibility profile
  CompatibilityProfile? get compatibilityProfile {
    if (!_isInitialized) {
      throw Exception('DeviceCompatibilityService not initialized');
    }
    return _compatibilityProfile;
  }

  /// Check if device requires special handling
  bool get requiresSpecialHandling => _compatibilityProfile?.requiresSpecialHandling ?? false;

  /// Check if device has aggressive power management
  bool get hasAggressivePowerManagement => _compatibilityProfile?.powerManagement.isAggressive ?? false;

  /// Get power management optimization steps
  List<String> get powerManagementSteps => _compatibilityProfile?.powerManagement.optimizationSteps ?? [];

  /// Public getter for initialization state
  bool get isInitialized => _isInitialized;

  /// Public getter for low-end device
  bool get isLowEndDevice => _performanceProfile.tier == PerformanceTier.low;
}

/// Device information model
class DeviceInfo {
  final String brand;
  final String manufacturer;
  final String model;
  final String device;
  final String product;
  final String androidVersion;
  final int sdkInt;
  final String? securityPatch;
  final bool isPhysicalDevice;
  final List<String> systemFeatures;
  final List<String> supportedAbis;
  final int totalMemory;
  final int availableMemory;

  DeviceInfo({
    required this.brand,
    required this.manufacturer,
    required this.model,
    required this.device,
    required this.product,
    required this.androidVersion,
    required this.sdkInt,
    this.securityPatch,
    required this.isPhysicalDevice,
    required this.systemFeatures,
    required this.supportedAbis,
    required this.totalMemory,
    required this.availableMemory,
  });

  factory DeviceInfo.fallback() => DeviceInfo(
    brand: 'unknown',
    manufacturer: 'unknown',
    model: 'unknown',
    device: 'unknown',
    product: 'unknown',
    androidVersion: '10',
    sdkInt: 29,
    isPhysicalDevice: true,
    systemFeatures: [],
    supportedAbis: [],
    totalMemory: 2 * 1024 * 1024 * 1024,
    availableMemory: 1 * 1024 * 1024 * 1024,
  );
}

/// Performance profile model
class PerformanceProfile {
  final PerformanceTier tier;
  final Map<String, dynamic> optimizations;

  PerformanceProfile({
    required this.tier,
    required this.optimizations,
  });
}

/// Performance tier enumeration
enum PerformanceTier { low, medium, high }

/// Compatibility profile model
class CompatibilityProfile {
  final List<DeviceQuirk> quirks;
  final PowerManagementProfile powerManagement;
  final NotificationSupport notificationSupport;
  final bool requiresSpecialHandling;

  CompatibilityProfile({
    required this.quirks,
    required this.powerManagement,
    required this.notificationSupport,
    required this.requiresSpecialHandling,
  });
}

/// Device quirks enumeration
enum DeviceQuirk {
  samsungBatteryOptimization,
  samsungEdgeGestures,
  aggressivePowerManagement,
  customNotificationSettings,
  backgroundAppKilling,
  miuiPermissions,
  autostartManagement,
  emuiPowerManagement,
  protectedApps,
  vivoBackgroundRestrictions,
  android12ExactAlarms,
  android13NotificationPermission,
}

/// Power management profile model
class PowerManagementProfile {
  final bool isAggressive;
  final List<String> optimizationSteps;

  PowerManagementProfile({
    required this.isAggressive,
    required this.optimizationSteps,
  });
}

/// Notification support model
class NotificationSupport {
  final bool supportsExactAlarms;
  final bool requiresPermission;
  final bool supportsChannels;
  final bool supportsGrouping;
  final List<String> limitations;

  NotificationSupport({
    required this.supportsExactAlarms,
    required this.requiresPermission,
    required this.supportsChannels,
    required this.supportsGrouping,
    required this.limitations,
  });
}
