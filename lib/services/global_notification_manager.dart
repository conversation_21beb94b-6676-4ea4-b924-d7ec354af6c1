import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../widgets/bottom_notification_card.dart';
import 'navigation_service.dart';

/// Global notification manager to handle notification display across app states
class GlobalNotificationManager {
  static final GlobalNotificationManager _instance = GlobalNotificationManager._internal();
  factory GlobalNotificationManager() => _instance;
  GlobalNotificationManager._internal();

  static const String _pendingNotificationKey = 'pending_notification';
  
  BuildContext? _currentContext;
  bool _isVideoPlayerActive = false;
  final _navigationService = NavigationService();

  /// Set the current context for displaying notifications
  void setContext(BuildContext context) {
    _currentContext = context;
  }

  /// Set video player state to avoid showing notifications during video playback
  void setVideoPlayerActive(bool isActive) {
    _isVideoPlayerActive = isActive;
  }

  /// Store a notification to be shown when the app becomes active
  Future<void> storePendingNotification({
    required String title,
    required String message,
    required String type,
    Map<String, dynamic>? data,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notification = {
        'title': title,
        'message': message,
        'type': type,
        'data': data ?? {},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      
      await prefs.setString(_pendingNotificationKey, jsonEncode(notification));
      print('GlobalNotificationManager: Stored pending notification: $title');
    } catch (e) {
      print('GlobalNotificationManager: Error storing pending notification: $e');
    }
  }

  /// Check for and display any pending notifications
  Future<void> checkAndShowPendingNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationJson = prefs.getString(_pendingNotificationKey);
      
      if (notificationJson != null) {
        final notification = jsonDecode(notificationJson);
        
        // Clear the pending notification
        await prefs.remove(_pendingNotificationKey);
        
        // Show the notification if context is available and not in video player
        if (_currentContext != null && !_isVideoPlayerActive) {
          await Future.delayed(const Duration(milliseconds: 500)); // Small delay for app to settle
          _showBottomNotificationCard(
            title: notification['title'],
            message: notification['message'],
            type: notification['type'],
            data: notification['data'],
          );
        }
        
        print('GlobalNotificationManager: Displayed pending notification: ${notification['title']}');
      }
    } catch (e) {
      print('GlobalNotificationManager: Error checking pending notifications: $e');
    }
  }

  /// Show a notification immediately if possible, otherwise store it
  Future<void> showNotification({
    required String title,
    required String message,
    required String type,
    Map<String, dynamic>? data,
  }) async {
    if (_currentContext != null && !_isVideoPlayerActive) {
      // Show immediately
      _showBottomNotificationCard(
        title: title,
        message: message,
        type: type,
        data: data,
      );
    } else {
      // Store for later
      await storePendingNotification(
        title: title,
        message: message,
        type: type,
        data: data,
      );
    }
  }

  /// Show bottom notification card
  void _showBottomNotificationCard({
    required String title,
    required String message,
    required String type,
    Map<String, dynamic>? data,
  }) {
    if (_currentContext == null || _isVideoPlayerActive) return;

    final iconData = _getIconForNotificationType(type);
    final iconColor = _getColorForNotificationType(type);

    BottomNotificationHelper.show(
      context: _currentContext!,
      title: title,
      message: message,
      icon: iconData,
      iconColor: iconColor,
      onTap: () {
        _handleNotificationTap(type, data);
      },
      duration: const Duration(seconds: 5),
    );
  }

  /// Get icon for notification type
  IconData _getIconForNotificationType(String type) {
    switch (type.toLowerCase()) {
      case 'workout':
      case 'workout_reminder':
        return Icons.fitness_center;
      case 'water':
      case 'water_reminder':
        return Icons.water_drop;
      case 'progress':
      case 'achievement':
        return Icons.emoji_events;
      case 'course':
      case 'video':
        return Icons.play_circle_outline;
      case 'general':
      case 'motivation':
        return Icons.star;
      default:
        return Icons.notifications;
    }
  }

  /// Get color for notification type
  Color _getColorForNotificationType(String type) {
    switch (type.toLowerCase()) {
      case 'workout':
      case 'workout_reminder':
        return const Color(0xFF10B981); // Green
      case 'water':
      case 'water_reminder':
        return const Color(0xFF3B82F6); // Blue
      case 'progress':
      case 'achievement':
        return const Color(0xFFF59E0B); // Amber
      case 'course':
      case 'video':
        return const Color(0xFF8B5CF6); // Purple
      case 'general':
      case 'motivation':
        return const Color(0xFF6366F1); // Indigo
      default:
        return const Color(0xFF6B7280); // Gray
    }
  }

  /// Handle notification tap actions
  void _handleNotificationTap(String type, Map<String, dynamic>? data) {
    if (_currentContext == null) return;
    
    switch (type.toLowerCase()) {
      case 'workout':
      case 'workout_reminder':
        // Navigate to workouts page
        _navigationService.pushNamed(NavigationService.home, arguments: {'tab': 1});
        break;
      case 'water':
      case 'water_reminder':
        // Navigate to water reminder page
        _navigationService.pushNamed(NavigationService.waterReminder);
        break;
      case 'progress':
      case 'achievement':
        // Navigate to progress page
        _navigationService.pushNamed(NavigationService.progress);
        break;
      case 'course':
      case 'video':
        // Navigate to specific course or home
        if (data != null && data['course_id'] != null) {
          // TODO: Navigate to specific course
          _navigationService.pushNamed(NavigationService.home);
        } else {
          _navigationService.pushNamed(NavigationService.home);
        }
        break;
      case 'general':
      case 'motivation':
        // Navigate to home or quotes
        _navigationService.pushNamed(NavigationService.home);
        break;
      default:
        // Default to home
        _navigationService.pushNamed(NavigationService.home);
        break;
    }
  }

  /// Clear any pending notifications (useful for logout)
  Future<void> clearPendingNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_pendingNotificationKey);
      print('GlobalNotificationManager: Cleared pending notifications');
    } catch (e) {
      print('GlobalNotificationManager: Error clearing pending notifications: $e');
    }
  }

  /// Handle notification from local notification response
  Future<void> handleNotificationFromResponse({
    required String title,
    required String message,
    required String type,
    Map<String, dynamic>? data,
  }) async {
    await showNotification(
      title: title,
      message: message,
      type: type,
      data: data,
    );
  }
}
