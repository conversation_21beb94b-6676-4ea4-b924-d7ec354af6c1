import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/water_reminder.dart';
import 'api_service.dart';

class WaterReminderService {
  final ApiService _apiService = ApiService();

  // Cache key for storing water reminder data locally
  static const String _cacheKey = 'water_reminder_data';

  // Cache expiration time (5 minutes)
  static const Duration _cacheExpiration = Duration(minutes: 5);

  // Last fetch timestamp
  DateTime? _lastFetchTime;

  // Cached water reminder
  WaterReminder? _cachedReminder;

  // Singleton instance
  static final WaterReminderService _instance = WaterReminderService._internal();

  final Map<String, dynamic> _settingsData = {};

  factory WaterReminderService() {
    return _instance;
  }

  WaterReminderService._internal();

  // Get water reminder data from backend (or local if backend not available)
  Future<WaterReminder?> getWaterReminder() async {
    // Always try to load from local storage first
    final localReminder = await _loadFromLocalStorage();
    if (localReminder != null) {
      _cachedReminder = localReminder;
      _lastFetchTime = DateTime.now();
      return localReminder;
    }

    try {
      // Get token for authentication
      final token = await _apiService.getToken();
      if (token == null) {
        debugPrint('💧 WaterReminderService: No token found. Cannot fetch water data.');
        throw Exception('Not authenticated');
      }

      // Debugging: Log the token (first few characters for security)
      debugPrint('💧 WaterReminderService: Using token: ${token.substring(0, 10)}...');

      // Get water intake data for today
      final intakeUri = Uri.parse('${ApiService.baseUrl}update_water_intake.php');
      debugPrint('💧 WaterReminderService: Fetching intake from: $intakeUri');
      final intakeResponse = await http.get(
        intakeUri,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      // Get reminder settings
      final settingsUri = Uri.parse('${ApiService.baseUrl}water_reminder.php');
      debugPrint('💧 WaterReminderService: Fetching settings from: $settingsUri');
      final settingsResponse = await http.get(
        settingsUri,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      debugPrint('💧 Water intake API status: ${intakeResponse.statusCode}');
      debugPrint('💧 Water settings API status: ${settingsResponse.statusCode}');
      debugPrint('💧 Water intake API body: ${intakeResponse.body}');
      debugPrint('💧 Water settings API body: ${settingsResponse.body}');

      if (intakeResponse.statusCode == 200) {
        final intakeData = jsonDecode(intakeResponse.body);

        // Process settings data
        TimeOfDay reminderTime = const TimeOfDay(hour: 9, minute: 0);
        bool isEnabled = true;

        if (settingsResponse.statusCode == 200) {
          final settingsData = jsonDecode(settingsResponse.body);
          reminderTime = TimeOfDay(
            hour: int.parse(settingsData['start_time']?.split(':')[0] ?? '9'),
            minute: int.parse(settingsData['start_time']?.split(':')[1] ?? '0'),
          );
          isEnabled = settingsData['is_active'] == 1;
          _settingsData.addAll(settingsData); // Store settings data
        }

        // Convert logs from API format
        final logs = (intakeData['logs'] as List<dynamic>?)?.map((log) {
          return WaterLog(
            id: log['id'],
            amount: log['amount'],
            timestamp: DateTime.parse(log['timestamp']),
          );
        }).toList() ?? [];

        // Create WaterReminder with synced data
        final reminder = WaterReminder(
          id: intakeData['user_id'] ?? 1,
          title: 'Water Reminder',
          message: 'Time to drink water!',
          time: reminderTime,
          isEnabled: isEnabled,
          targetIntake: intakeData['target_intake'] ?? 2000,
          currentIntake: intakeData['current_intake'] ?? 0,
          logs: logs,
          lastUpdated: DateTime.now(),
          reminderInterval: _settingsData['interval_hours'] ?? 2,
          isNotificationsEnabled: _settingsData['is_active'] == 1,
        );

        _cachedReminder = reminder;
        _lastFetchTime = DateTime.now();
        _saveToLocalStorage(reminder);

        debugPrint('💧 Water data synced from backend: ${reminder.currentIntake}ml/${reminder.targetIntake}ml');
        return reminder;
      } else {
        // Try to load from local storage if API fails
        final localReminder = await _loadFromLocalStorage();
        if (localReminder != null) {
          debugPrint('💧 Using local water data: ${localReminder.currentIntake}ml/${localReminder.targetIntake}ml');
          return localReminder;
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error fetching water reminder: $e');

      // Try to load from local storage as fallback
      final localReminder = await _loadFromLocalStorage();
      if (localReminder != null) {
        _cachedReminder = localReminder;
        debugPrint('💧 Using local water data: ${localReminder.currentIntake}ml/${localReminder.targetIntake}ml');
        return localReminder;
      }

      return null;
    }
  }

  // Get or create user-managed water reminder (for settings page)
  Future<WaterReminder> getUserWaterReminder() async {
    // First try to get admin-managed reminder
    final adminReminder = await getWaterReminder();
    if (adminReminder != null) {
      return adminReminder;
    }

    // If no admin reminder, check for user settings in local storage
    final userReminder = await _loadUserSettingsFromLocalStorage();
    if (userReminder != null) {
      return userReminder;
    }

    // Create default user reminder
    return _createDefaultReminder();
  }

  // Save user settings (when user configures their own settings)
  Future<void> saveUserSettings(WaterReminder reminder) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonData = jsonEncode(reminder.toJson());
      await prefs.setString('${_cacheKey}_user', jsonData);

      // Update cache
      _cachedReminder = reminder;
      _lastFetchTime = DateTime.now();

      // Sync settings with backend if authenticated
      await _syncSettings(reminder);

    } catch (e) {
      debugPrint('Error saving user water reminder settings: $e');
    }
  }

  // Sync settings with backend
  Future<void> _syncSettings(WaterReminder reminder) async {
    try {
      final token = await _apiService.getToken();
      if (token == null) return; // Not authenticated, skip sync

      final response = await http.put(
        Uri.parse('${ApiService.baseUrl}water_reminder.php'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'target_intake': reminder.targetIntake,
          'interval_hours': reminder.reminderInterval,
          'is_active': reminder.isNotificationsEnabled ? 1 : 0,
          'start_time': reminder.formattedTime,
        }),
      );

      if (response.statusCode == 200) {
        debugPrint('💧 Settings synced with backend: interval=${reminder.reminderInterval}h');
        // Update local cache after successful sync
        _cachedReminder = reminder;
        _lastFetchTime = DateTime.now();
        await _saveToLocalStorage(reminder);
      } else {
        debugPrint('💧 Failed to sync settings: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('💧 Error syncing settings: $e');
    }
  }

  // Sync target intake with backend
  Future<void> _syncTargetIntake(int targetIntake) async {
    try {
      final token = await _apiService.getToken();
      if (token == null) return; // Not authenticated, skip sync

      debugPrint('💧 Syncing target intake. Token: ${token.substring(0, 10)}..., URL: ${ApiService.baseUrl}update_water_intake.php');

      final response = await http.put(
        Uri.parse('${ApiService.baseUrl}update_water_intake.php'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'target_intake': targetIntake,
        }),
      );

      if (response.statusCode == 200) {
        debugPrint('💧 Target intake synced with backend: ${targetIntake}ml');
      } else {
        debugPrint('💧 Failed to sync target intake: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('💧 Error syncing target intake: $e');
    }
  }

  // Load user settings from local storage
  Future<WaterReminder?> _loadUserSettingsFromLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonData = prefs.getString('${_cacheKey}_user');

      if (jsonData != null) {
        final reminderJson = jsonDecode(jsonData);
        return WaterReminder.fromJson(reminderJson);
      }

      return null;
    } catch (e) {
      print('Error loading user water reminder settings: $e');
      return null;
    }
  }

  // Check if user has configured their own settings
  Future<bool> hasUserSettings() async {
    final userReminder = await _loadUserSettingsFromLocalStorage();
    return userReminder != null;
  }

  // Force refresh water data from backend (ignores cache)
  Future<WaterReminder?> forceRefreshWaterData() async {
    _cachedReminder = null;
    _lastFetchTime = null;
    return await getWaterReminder();
  }

  // Update water intake (local only, ignore backend)
  Future<WaterReminder?> updateWaterIntake(int amount) async {
    // Get current reminder (admin or user)
    final currentReminder = await getUserWaterReminder();

    // Create local log entry
    final newLog = WaterLog(
      id: DateTime.now().millisecondsSinceEpoch,
      amount: amount,
      timestamp: DateTime.now(),
    );

    // Update current intake and logs locally
    final updatedIntake = currentReminder.currentIntake + amount;
    final updatedLogs = [...currentReminder.logs, newLog];

    // Create updated reminder
    final updatedReminder = currentReminder.copyWith(
      currentIntake: updatedIntake,
      logs: updatedLogs,
      lastUpdated: DateTime.now(),
    );

    // Update cache
    _cachedReminder = updatedReminder;
    await _saveToLocalStorage(updatedReminder);

    debugPrint('💧 Water intake updated locally: +${amount}ml = ${updatedIntake}ml/${currentReminder.targetIntake}ml');
    return updatedReminder;
  }

  // Reset water intake (typically called at midnight)
  Future<WaterReminder?> resetWaterIntake() async {
    try {
      // Get current reminder (admin or user)
      final currentReminder = await getUserWaterReminder();

      // Get token for authentication
      final token = await _apiService.getToken();
      if (token == null) {
        throw Exception('Not authenticated');
      }

      debugPrint('💧 Resetting water intake. Token: ${token.substring(0, 10)}..., URL: ${ApiService.baseUrl}reset_water_intake.php');

      // Create updated reminder with reset intake and logs
      final updatedReminder = currentReminder.copyWith(
        currentIntake: 0,
        logs: [],
        lastUpdated: DateTime.now(),
      );

      // Make API request to reset
      final response = await http.post(
        Uri.parse('${ApiService.baseUrl}reset_water_intake.php'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      // Log response for debugging
      print('Reset water intake API status: ${response.statusCode}');
      print('Reset water intake API body: ${response.body}');

      // Update cache regardless of API response
      _cachedReminder = updatedReminder;
      _saveToLocalStorage(updatedReminder);

      return updatedReminder;
    } catch (e) {
      print('Error resetting water intake: $e');

      // If API fails, update local cache only
      if (_cachedReminder != null) {
        final updatedReminder = _cachedReminder!.copyWith(
          currentIntake: 0,
          logs: [],
          lastUpdated: DateTime.now(),
        );

        _cachedReminder = updatedReminder;
        _saveToLocalStorage(updatedReminder);

        return updatedReminder;
      }

      // If no cached reminder, create a default one
      return _createDefaultReminder();
    }
  }

  // Save reminder to local storage
  Future<void> _saveToLocalStorage(WaterReminder reminder) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonData = jsonEncode(reminder.toJson());
      await prefs.setString(_cacheKey, jsonData);
    } catch (e) {
      print('Error saving water reminder to local storage: $e');
    }
  }

  // Load reminder from local storage
  Future<WaterReminder?> _loadFromLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonData = prefs.getString(_cacheKey);

      if (jsonData != null) {
        final reminderJson = jsonDecode(jsonData);
        return WaterReminder.fromJson(reminderJson);
      }

      return null;
    } catch (e) {
      print('Error loading water reminder from local storage: $e');
      return null;
    }
  }

  // Clear local storage
  Future<void> _clearLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
    } catch (e) {
      print('Error clearing water reminder from local storage: $e');
    }
  }

  // Create a default water reminder
  WaterReminder _createDefaultReminder() {
    return WaterReminder(
      id: 1,
      title: 'Water Reminder',
      message: 'Time to drink water!',
      time: const TimeOfDay(hour: 9, minute: 0),
      isEnabled: true,
      targetIntake: 2000, // Default target: 2 liters (2000 ml)
      currentIntake: 0,
      logs: [],
      lastUpdated: DateTime.now(),
      reminderInterval: 2,
      isNotificationsEnabled: true,
    );
  }

  // Check if water intake needs to be reset (called at midnight)
  Future<void> checkAndResetWaterIntake() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastResetDate = prefs.getString('last_water_reset_date');
      final now = DateTime.now();
      
      // If no last reset date or last reset was not today, reset the intake
      if (lastResetDate == null || DateTime.parse(lastResetDate).day != now.day) {
        await resetWaterIntake();
        // Save the reset date
        await prefs.setString('last_water_reset_date', now.toIso8601String());
        debugPrint('💧 Water intake reset at midnight');
      }
    } catch (e) {
      debugPrint('Error checking water intake reset: $e');
    }
  }

  Map<String, dynamic> get settingsData => _settingsData;
}
