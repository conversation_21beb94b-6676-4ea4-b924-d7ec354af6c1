import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

/// Comprehensive water goal management service
/// Handles persistent goal settings, unit conversions, and local storage
class WaterGoalService {
  static const String _goalKey = 'water_goal_settings';
  static const String _unitKey = 'water_unit_preference';
  static const String _lastGoalUpdateKey = 'water_goal_last_update';
  
  // Singleton pattern
  static final WaterGoalService _instance = WaterGoalService._internal();
  factory WaterGoalService() => _instance;
  WaterGoalService._internal();

  /// Water goal settings model
  WaterGoalSettings? _cachedSettings;
  
  /// Get current water goal settings
  Future<WaterGoalSettings> getGoalSettings() async {
    if (_cachedSettings != null) {
      return _cachedSettings!;
    }
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_goalKey);
      
      if (settingsJson != null) {
        final settings = WaterGoalSettings.fromJson(jsonDecode(settingsJson));
        _cachedSettings = settings;
        return settings;
      }
      
      // Return default settings if none exist
      final defaultSettings = WaterGoalSettings.defaultSettings();
      await saveGoalSettings(defaultSettings);
      return defaultSettings;
    } catch (e) {
      debugPrint('Error loading water goal settings: $e');
      return WaterGoalSettings.defaultSettings();
    }
  }
  
  /// Save water goal settings
  Future<void> saveGoalSettings(WaterGoalSettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_goalKey, jsonEncode(settings.toJson()));
      await prefs.setString(_lastGoalUpdateKey, DateTime.now().toIso8601String());
      
      _cachedSettings = settings;
      debugPrint('💧 Water goal settings saved: ${settings.goalInMl}ml (${settings.preferredUnit.displayName})');
    } catch (e) {
      debugPrint('Error saving water goal settings: $e');
      rethrow;
    }
  }
  
  /// Update daily water goal
  Future<void> updateDailyGoal(int goalInMl) async {
    final currentSettings = await getGoalSettings();
    final updatedSettings = currentSettings.copyWith(
      goalInMl: goalInMl,
      lastUpdated: DateTime.now(),
    );
    await saveGoalSettings(updatedSettings);
  }
  
  /// Update preferred unit
  Future<void> updatePreferredUnit(WaterUnit unit) async {
    final currentSettings = await getGoalSettings();
    final updatedSettings = currentSettings.copyWith(
      preferredUnit: unit,
      lastUpdated: DateTime.now(),
    );
    await saveGoalSettings(updatedSettings);
  }
  
  /// Get goal in preferred unit
  Future<double> getGoalInPreferredUnit() async {
    final settings = await getGoalSettings();
    return settings.getGoalInUnit(settings.preferredUnit);
  }
  
  /// Convert ml to liters
  static double mlToLiters(int ml) => ml / 1000.0;
  
  /// Convert liters to ml
  static int litersToMl(double liters) => (liters * 1000).round();
  
  /// Format goal display text
  static String formatGoalDisplay(int goalInMl, WaterUnit unit) {
    switch (unit) {
      case WaterUnit.milliliters:
        return '${goalInMl}ml';
      case WaterUnit.liters:
        final liters = mlToLiters(goalInMl);
        return liters == liters.toInt() 
            ? '${liters.toInt()}L' 
            : '${liters.toStringAsFixed(1)}L';
    }
  }
  
  /// Get common goal presets in ml
  static List<int> getGoalPresets() => [1000, 1500, 2000, 2500, 3000];
  
  /// Get quick adjustment amounts in ml
  static List<int> getQuickAdjustments() => [250, 500, 750];
  
  /// Validate goal range
  static bool isValidGoal(int goalInMl) => goalInMl >= 500 && goalInMl <= 5000;
  
  /// Clear all settings (for testing/reset)
  Future<void> clearSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_goalKey);
      await prefs.remove(_unitKey);
      await prefs.remove(_lastGoalUpdateKey);
      _cachedSettings = null;
    } catch (e) {
      debugPrint('Error clearing water goal settings: $e');
    }
  }
}

/// Water unit enumeration
enum WaterUnit {
  milliliters,
  liters,
}

extension WaterUnitExtension on WaterUnit {
  String get displayName {
    switch (this) {
      case WaterUnit.milliliters:
        return 'ml';
      case WaterUnit.liters:
        return 'L';
    }
  }
  
  String get fullName {
    switch (this) {
      case WaterUnit.milliliters:
        return 'Milliliters';
      case WaterUnit.liters:
        return 'Liters';
    }
  }
}

/// Water goal settings model
class WaterGoalSettings {
  final int goalInMl;
  final WaterUnit preferredUnit;
  final DateTime lastUpdated;
  final bool isCustomGoal;
  
  const WaterGoalSettings({
    required this.goalInMl,
    required this.preferredUnit,
    required this.lastUpdated,
    this.isCustomGoal = false,
  });
  
  /// Create default settings
  factory WaterGoalSettings.defaultSettings() {
    return WaterGoalSettings(
      goalInMl: 2000, // 2 liters default
      preferredUnit: WaterUnit.liters,
      lastUpdated: DateTime.now(),
      isCustomGoal: false,
    );
  }
  
  /// Create from JSON
  factory WaterGoalSettings.fromJson(Map<String, dynamic> json) {
    return WaterGoalSettings(
      goalInMl: json['goalInMl'] ?? 2000,
      preferredUnit: WaterUnit.values[json['preferredUnit'] ?? 1],
      lastUpdated: DateTime.parse(json['lastUpdated'] ?? DateTime.now().toIso8601String()),
      isCustomGoal: json['isCustomGoal'] ?? false,
    );
  }
  
  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'goalInMl': goalInMl,
      'preferredUnit': preferredUnit.index,
      'lastUpdated': lastUpdated.toIso8601String(),
      'isCustomGoal': isCustomGoal,
    };
  }
  
  /// Copy with modifications
  WaterGoalSettings copyWith({
    int? goalInMl,
    WaterUnit? preferredUnit,
    DateTime? lastUpdated,
    bool? isCustomGoal,
  }) {
    return WaterGoalSettings(
      goalInMl: goalInMl ?? this.goalInMl,
      preferredUnit: preferredUnit ?? this.preferredUnit,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      isCustomGoal: isCustomGoal ?? this.isCustomGoal,
    );
  }
  
  /// Get goal in specified unit
  double getGoalInUnit(WaterUnit unit) {
    switch (unit) {
      case WaterUnit.milliliters:
        return goalInMl.toDouble();
      case WaterUnit.liters:
        return WaterGoalService.mlToLiters(goalInMl);
    }
  }
  
  /// Get formatted display text
  String get displayText => WaterGoalService.formatGoalDisplay(goalInMl, preferredUnit);
  
  /// Check if goal is a common preset
  bool get isPresetGoal => WaterGoalService.getGoalPresets().contains(goalInMl);
  
  @override
  String toString() => 'WaterGoalSettings(${displayText})';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WaterGoalSettings &&
        other.goalInMl == goalInMl &&
        other.preferredUnit == preferredUnit;
  }
  
  @override
  int get hashCode => goalInMl.hashCode ^ preferredUnit.hashCode;
}
