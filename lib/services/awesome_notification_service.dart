import 'dart:io';
import 'dart:convert';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'global_notification_manager.dart';

class EnhancedNotificationService {
  static final EnhancedNotificationService _instance = EnhancedNotificationService._internal();
  factory EnhancedNotificationService() => _instance;
  EnhancedNotificationService._internal();

  bool _isInitialized = false;

  // Notification IDs
  static const int waterReminderId = 1000;
  static const int workoutReminderId = 2000;
  static const int customWorkoutReminderId = 3000;
  static const int testNotificationId = 9999;

  // Notification Channels
  static const String waterChannelId = 'water_reminder_channel';
  static const String workoutChannelId = 'workout_reminder_channel';
  static const String workoutVideosChannelId = 'workout_videos_channel';
  static const String generalChannelId = 'general_channel';
  static const String debugChannelId = 'scheduled';
  
  // SharedPreferences keys
  static const String _workoutNotificationsKey = 'workout_notifications_enabled';
  static const String _waterNotificationsKey = 'water_notifications_enabled';
  static const String _workoutTimeKey = 'workout_reminder_time';
  static const String _waterIntervalKey = 'water_reminder_interval';
  static const String _customWorkoutTimesKey = 'custom_workout_times';

  // Motivational water reminder messages
  static const List<String> _motivationalMessages = [
    "Stay hydrated! Your body will thank you.",
    "A glass of water now keeps you energized!",
    "Keep going! Every sip counts toward your goal.",
    "Hydration is key to a healthy day!",
    "Drink up! You're doing great.",
    "Water helps you focus. Take a sip!",
    "Almost there! Keep drinking water.",
    "Cheers to your health—have some water!",
    "Small sips, big results. Stay on track!",
    "You got this! Hydrate and conquer your day.",
    "Your daily goal is within reach. Drink some water!",
    "Refresh yourself—it's water time!",
    "Hydration boosts your mood. Take a drink!",
    "Keep your body happy. Drink water!",
    "Every drop counts. Stay hydrated!",
  ];

  bool get isInitialized => _isInitialized;

  /// Initialize Enhanced Notifications
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('🔔 Initializing Enhanced Notifications...');

      // Initialize timezone data
      tz_data.initializeTimeZones();

      // Initialize AwesomeNotifications
      await AwesomeNotifications().initialize(
        null, // Use default app icon
        [
          NotificationChannel(
            channelKey: waterChannelId,
            channelName: 'Water Reminders',
            channelDescription: 'Notifications for water intake reminders',
            defaultColor: const Color(0xFF06B6D4),
            ledColor: const Color(0xFF06B6D4),
            importance: NotificationImportance.High,
            channelShowBadge: true,
            onlyAlertOnce: false,
            playSound: true,
            criticalAlerts: false,
            icon: 'resource://drawable/ic_notification',
          ),
          NotificationChannel(
            channelKey: workoutChannelId,
            channelName: 'Workout Reminders',
            channelDescription: 'Notifications for workout reminders',
            defaultColor: const Color(0xFFFF6B35),
            ledColor: const Color(0xFFFF6B35),
            importance: NotificationImportance.High,
            channelShowBadge: true,
            onlyAlertOnce: false,
            playSound: true,
            criticalAlerts: false,
            icon: 'resource://drawable/ic_notification',
          ),
          NotificationChannel(
            channelKey: workoutVideosChannelId,
            channelName: 'Workout Videos',
            channelDescription: 'Notifications for new workout videos and unlocks',
            defaultColor: const Color(0xFF8B5CF6),
            ledColor: const Color(0xFF8B5CF6),
            importance: NotificationImportance.High,
            channelShowBadge: true,
            onlyAlertOnce: false,
            playSound: true,
            criticalAlerts: false,
            icon: 'resource://drawable/ic_notification',
          ),
          NotificationChannel(
            channelKey: generalChannelId,
            channelName: 'General Notifications',
            channelDescription: 'General app notifications',
            defaultColor: Colors.green,
            ledColor: Colors.green,
            importance: NotificationImportance.Default,
            channelShowBadge: true,
            onlyAlertOnce: false,
            playSound: true,
          ),
          NotificationChannel(
            channelKey: debugChannelId,
            channelName: 'Debug Notifications',
            channelDescription: 'Debug and test notifications',
            defaultColor: Colors.purple,
            ledColor: Colors.purple,
            importance: NotificationImportance.High,
            channelShowBadge: true,
            onlyAlertOnce: false,
            playSound: true,
          ),
        ],
        debug: true,
      );

      _isInitialized = true;
      print('✅ Enhanced Notifications initialized successfully');
    } catch (e) {
      print('❌ Error initializing Enhanced Notifications: $e');
      rethrow;
    }
  }

  /// Create notification channels for Android
  Future<void> _createNotificationChannels() async {
    // Channels are created during initialization
  }

  /// Request notification permissions with explanation dialog
  Future<bool> requestPermissionsWithExplanation(BuildContext context) async {
    try {
      print('🔔 Requesting notification permissions with explanation...');

      // First check if we already have permission
      bool isAllowed = await AwesomeNotifications().isNotificationAllowed();
      if (isAllowed) {
        return true;
      }

      // Show explanation dialog
      final bool? shouldRequest = await showDialog<bool>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Enable Notifications'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'To help you stay hydrated and achieve your fitness goals, we need to send you notifications for:',
                ),
                const SizedBox(height: 12),
                _buildPermissionItem(
                  context,
                  Icons.water_drop,
                  'Water Reminders',
                  'Regular reminders to drink water throughout the day',
                ),
                const SizedBox(height: 8),
                _buildPermissionItem(
                  context,
                  Icons.fitness_center,
                  'Workout Reminders',
                  'Scheduled reminders for your workout sessions',
                ),
                const SizedBox(height: 8),
                _buildPermissionItem(
                  context,
                  Icons.video_library,
                  'New Content',
                  'Updates about new workout videos and features',
                ),
                const SizedBox(height: 16),
                Text(
                  'You can change these settings anytime in the app settings.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Not Now'),
              ),
              FilledButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Enable Notifications'),
              ),
            ],
          );
        },
      );

      if (shouldRequest != true) {
        return false;
      }

      // Request permission
      isAllowed = await AwesomeNotifications().requestPermissionToSendNotifications();

      if (isAllowed) {
        // Request additional Android permissions
        if (Platform.isAndroid) {
          // Request exact alarm permission for Android 12+
          await Permission.scheduleExactAlarm.request();

          // Request ignore battery optimization
          await Permission.ignoreBatteryOptimizations.request();

          // Request notification policy access
          await Permission.accessNotificationPolicy.request();
        }

        // Show success message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Notifications enabled successfully!'),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } else {
        // Show settings prompt if permission denied
        if (context.mounted) {
          final bool? openSettings = await showDialog<bool>(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: const Text('Permission Required'),
                content: const Text(
                  'To receive water reminders and other important notifications, please enable notifications in your device settings.',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: const Text('Cancel'),
                  ),
                  FilledButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    child: const Text('Open Settings'),
                  ),
                ],
              );
            },
          );

          if (openSettings == true) {
            await openAppSettings();
          }
        }
      }

      return isAllowed;
    } catch (e) {
      print('❌ Error requesting notification permissions: $e');
      return false;
    }
  }

  Widget _buildPermissionItem(
    BuildContext context,
    IconData icon,
    String title,
    String description,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: Theme.of(context).colorScheme.primary),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Request notification permissions (legacy method)
  Future<bool> requestPermissions() async {
    try {
      print('🔔 Requesting notification permissions...');

      // Request basic notification permission
      bool isAllowed = await AwesomeNotifications().isNotificationAllowed();

      if (!isAllowed) {
        isAllowed = await AwesomeNotifications().requestPermissionToSendNotifications();
      }

      if (isAllowed) {
        // Request additional Android permissions
        if (Platform.isAndroid) {
          // Request exact alarm permission for Android 12+
          await Permission.scheduleExactAlarm.request();

          // Request ignore battery optimization
          await Permission.ignoreBatteryOptimizations.request();

          // Request notification policy access
          await Permission.accessNotificationPolicy.request();
        }
      }

      print('🔔 Notification permissions result: $isAllowed');
      return isAllowed;
    } catch (e) {
      print('❌ Error requesting notification permissions: $e');
      return false;
    }
  }

  /// Check if notifications are allowed
  Future<bool> hasPermissions() async {
    try {
      return await AwesomeNotifications().isNotificationAllowed();
    } catch (e) {
      print('❌ Error checking notification permissions: $e');
      return false;
    }
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    try {
      await AwesomeNotifications().cancelAll();
      print('🔔 All notifications cancelled');
    } catch (e) {
      print('❌ Error cancelling all notifications: $e');
    }
  }

  /// Cancel notification by ID
  Future<void> cancelNotification(int id) async {
    try {
      await AwesomeNotifications().cancel(id);
      print('🔔 Notification $id cancelled');
    } catch (e) {
      print('❌ Error cancelling notification $id: $e');
    }
  }

  /// Get scheduled notifications
  Future<List<Map<String, dynamic>>> getScheduledNotifications() async {
    try {
      final notifications = await AwesomeNotifications().listScheduledNotifications();
      return notifications.map((n) => {
        'id': n.content?.id,
        'title': n.content?.title,
        'body': n.content?.body,
        'channelKey': n.content?.channelKey,
        'scheduledDate': n.schedule?.toString(),
      }).toList();
    } catch (e) {
      print('❌ Error getting scheduled notifications: $e');
      return [];
    }
  }

  // ==================== WATER REMINDER METHODS ====================

  /// Schedule water reminders
  Future<void> scheduleWaterReminders({
    required int dailyTarget,
    int intervalHours = 2,
  }) async {
    try {
      // Cancel any existing water reminders first
      await cancelWaterReminders();

      // Get current time
      final now = DateTime.now();
      
      // Calculate start time (next hour)
      final startTime = DateTime(
        now.year,
        now.month,
        now.day,
        now.hour + 1,
        0, // Start at the beginning of the hour
      );

      // Schedule notifications for the next 24 hours
      int messageIndex = now.day % _motivationalMessages.length;
      for (int i = 0; i < 24; i += intervalHours) {
        final scheduledTime = startTime.add(Duration(hours: i));
        
        // Skip if the time is in the past
        if (scheduledTime.isBefore(now)) continue;

        // Pick a different message for each notification
        final message = _motivationalMessages[(messageIndex + i) % _motivationalMessages.length];

        await showScheduledNotification(
          id: waterReminderId + i, // Unique ID for each notification
          title: 'Time to Hydrate! 💧',
          body: message + ' Your daily goal: ${dailyTarget}ml',
          scheduledDate: scheduledTime,
          channelKey: waterChannelId,
          repeats: true,
        );

        print('🔔 Water reminder scheduled for [32m${scheduledTime.toString()}[0m with message: $message');
      }

      print('🔔 Water reminders scheduled successfully');
    } catch (e) {
      print('❌ Error scheduling water reminders: $e');
      rethrow;
    }
  }

  /// Show a single water reminder notification (for testing or immediate use)
  Future<void> showWaterReminderNotification({int intervalInSeconds = 0}) async {
    try {
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: testNotificationId,
          channelKey: waterChannelId,
          title: '💧 Test Water Reminder!',
          body: 'This is a test notification for your water reminder.',
          notificationLayout: NotificationLayout.Default,
          autoDismissible: true,
          wakeUpScreen: true,
          category: NotificationCategory.Reminder,
          criticalAlert: true,
          actionType: ActionType.Default,
          displayOnForeground: true,
          displayOnBackground: true,
        ),
        schedule: intervalInSeconds > 0
            ? NotificationCalendar(
                second: DateTime.now().second + intervalInSeconds,
                millisecond: 0,
                repeats: false,
                allowWhileIdle: true,
                preciseAlarm: true,
              )
            : null, // If no interval, show immediately
      );
      print('🔔 Test water reminder notification shown');
    } catch (e) {
      print('❌ Error showing test water reminder notification: $e');
      rethrow;
    }
  }

  /// Cancel all water reminders
  Future<void> cancelWaterReminders() async {
    try {
      // Cancel only water reminder notifications (IDs 1000-1023)
      for (int i = 0; i < 24; i += 1) {
        await AwesomeNotifications().cancel(waterReminderId + i * 2); // IDs: 1000, 1002, 1004, ...
        await AwesomeNotifications().cancel(waterReminderId + i); // Also cover IDs: 1000, 1001, 1002, ...
      }
      print('🔔 All water reminders cancelled');
    } catch (e) {
      print('❌ Error cancelling water reminders: $e');
      rethrow;
    }
  }

  /// Set water notification enabled status in shared preferences
  Future<void> setWaterNotificationEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_waterNotificationsKey, enabled);
      if (!enabled) {
        // Clear any stored interval when disabling notifications
        await prefs.remove(_waterIntervalKey);
        print('🔔 Water notification status set to disabled. Stored interval cleared.');
      } else {
        print('🔔 Water notification status set to enabled.');
      }
    } catch (e) {
      print('❌ Error setting water notification enabled status: $e');
      rethrow;
    }
  }

  /// Get water notification enabled status from shared preferences
  Future<bool> isWaterNotificationEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_waterNotificationsKey) ?? true; // Default to true
    } catch (e) {
      print('❌ Error getting water notification enabled status: $e');
      return true;
    }
  }

  /// Set water reminder interval in shared preferences (in hours)
  Future<void> setWaterReminderInterval(int intervalHours) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_waterIntervalKey, intervalHours);
      print('🔔 Water reminder interval set to $intervalHours hours');
    } catch (e) {
      print('❌ Error setting water reminder interval: $e');
      rethrow;
    }
  }

  /// Get water reminder interval from shared preferences (in hours)
  Future<int> getWaterReminderInterval() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_waterIntervalKey) ?? 2; // Default to 2 hours
    } catch (e) {
      print('❌ Error getting water reminder interval: $e');
      return 2;
    }
  }

  // Helper to get today's water intake (placeholder - replace with actual logic)
  Future<int> _getTodaysWaterIntake() async {
    // This is a placeholder. You should replace this with actual logic
    // to fetch today's water intake from your data source (e.g., WaterReminderProvider or database).
    return 0; // Return 0 for now as a placeholder
  }

  // ==================== WORKOUT REMINDER METHODS ====================

  /// Schedule workout reminders at a specific time
  Future<void> scheduleWorkoutReminders(TimeOfDay time) async {
    try {
      await cancelWorkoutReminders();

      final now = DateTime.now();
      final scheduledTime = tz.TZDateTime(
        tz.local,
        now.year,
        now.month,
        now.day,
        time.hour,
        time.minute,
        0,
      );

      // If the scheduled time is in the past, schedule for tomorrow
      final nextScheduledTime = scheduledTime.isBefore(now)
          ? scheduledTime.add(const Duration(days: 1))
          : scheduledTime;

      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: workoutReminderId,
          channelKey: workoutChannelId,
          title: '💪 Workout Time!',
          body: 'It\'s time for your daily workout. Stay consistent!',
          notificationLayout: NotificationLayout.Default,
          autoDismissible: true,
          wakeUpScreen: true,
          category: NotificationCategory.Reminder,
          criticalAlert: true,
        ),
        schedule: NotificationCalendar(
          year: nextScheduledTime.year,
          month: nextScheduledTime.month,
          day: nextScheduledTime.day,
          hour: nextScheduledTime.hour,
          minute: nextScheduledTime.minute,
          second: 0,
          repeats: true,
          allowWhileIdle: true,
          preciseAlarm: true,
        ),
      );

      // Save workout reminder time
      await _saveWorkoutReminderTime(time);

      print('🔔 Workout reminders scheduled daily at ${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}');
    } catch (e) {
      print('❌ Error scheduling workout reminders: $e');
    }
  }

  /// Test workout reminder for today (always schedules for 10 seconds from now)
  Future<void> testWorkoutReminderToday(TimeOfDay time) async {
    try {
      final now = DateTime.now();
      final testScheduledTime = now.add(const Duration(seconds: 10));
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: workoutReminderId + 9999, // Unique test ID
          channelKey: workoutChannelId,
          title: '🧪 Test: Workout Reminder',
          body: 'This is a test notification for your workout reminder!',
          notificationLayout: NotificationLayout.Default,
          autoDismissible: true,
          wakeUpScreen: true,
          category: NotificationCategory.Reminder,
          criticalAlert: true,
        ),
        schedule: NotificationCalendar(
          year: testScheduledTime.year,
          month: testScheduledTime.month,
          day: testScheduledTime.day,
          hour: testScheduledTime.hour,
          minute: testScheduledTime.minute,
          second: testScheduledTime.second,
          repeats: false,
          allowWhileIdle: true,
          preciseAlarm: true,
        ),
      );
      print('🧪 Workout test notification scheduled for ${testScheduledTime.toString()} (ID: ${workoutReminderId + 9999})');
    } catch (e) {
      print('❌ Error scheduling workout test notification: $e');
      rethrow;
    }
  }

  /// Cancel workout reminders
  Future<void> cancelWorkoutReminders() async {
    try {
      await AwesomeNotifications().cancel(workoutReminderId);
      await _saveWorkoutReminderTime(null); // Clear stored time
      print('🔔 Workout reminders cancelled');
    } catch (e) {
      print('❌ Error cancelling workout reminders: $e');
    }
  }

  /// Get workout reminder time from shared preferences
  Future<TimeOfDay?> getWorkoutReminderTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timeString = prefs.getString(_workoutTimeKey);
      if (timeString != null) {
        final parts = timeString.split(':');
        return TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
      }
      return null;
    } catch (e) {
      print('❌ Error getting workout reminder time: $e');
      return null;
    }
  }

  Future<void> _saveWorkoutReminderTime(TimeOfDay? time) async {
    final prefs = await SharedPreferences.getInstance();
    if (time != null) {
      await prefs.setString(_workoutTimeKey, '${time.hour}:${time.minute}');
    } else {
      await prefs.remove(_workoutTimeKey);
    }
  }

  /// Get workout notification enabled status from shared preferences
  Future<bool> isWorkoutNotificationEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_workoutNotificationsKey) ?? false; // Default to false
    } catch (e) {
      print('❌ Error getting workout notification enabled status: $e');
      return false;
    }
  }

  /// Set workout notification enabled status in shared preferences
  Future<void> setWorkoutNotificationEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_workoutNotificationsKey, enabled);
      print('🔔 Workout notifications ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      print('❌ Error setting workout notification enabled status: $e');
      rethrow;
    }
  }

  /// Schedule custom workout reminders (one-time or daily)
  Future<void> scheduleCustomWorkoutReminder({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    bool repeats = false,
  }) async {
    try {
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: id,
          channelKey: workoutVideosChannelId, // Changed to workoutVideosChannelId as per prior discussion
          title: title,
          body: body,
          notificationLayout: NotificationLayout.Default,
          autoDismissible: true,
          wakeUpScreen: true,
          category: NotificationCategory.Reminder,
          criticalAlert: true,
        ),
        schedule: NotificationCalendar(
          year: scheduledDate.year,
          month: scheduledDate.month,
          day: scheduledDate.day,
          hour: scheduledDate.hour,
          minute: scheduledDate.minute,
          second: scheduledDate.second,
          repeats: repeats,
          allowWhileIdle: true,
          preciseAlarm: true,
        ),
      );
      // Save custom workout time
      _saveCustomWorkoutReminderTime(id, scheduledDate, repeats);
      print('🔔 Custom workout reminder scheduled: $title at $scheduledDate');
    } catch (e) {
      print('❌ Error scheduling custom workout reminder: $e');
    }
  }

  /// Cancel a specific custom workout reminder
  Future<void> cancelCustomWorkoutReminder(int id) async {
    try {
      await AwesomeNotifications().cancel(id);
      _deleteCustomWorkoutReminderTime(id);
      print('🔔 Custom workout reminder $id cancelled');
    } catch (e) {
      print('❌ Error cancelling custom workout reminder $id: $e');
    }
  }

  /// Get all scheduled custom workout reminders
  Future<List<Map<String, dynamic>>> getCustomWorkoutTimes() async { // Renamed from getScheduledCustomWorkoutReminders
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? customTimesJson = prefs.getString(_customWorkoutTimesKey);
      if (customTimesJson == null) {
        return [];
      }
      final List<dynamic> decodedList = jsonDecode(customTimesJson);
      return decodedList.map((e) => e as Map<String, dynamic>).toList();
    } catch (e) {
      print('❌ Error getting custom workout reminder times: $e');
      return [];
    }
  }

  // Save custom workout reminder time to Shared Preferences
  Future<void> _saveCustomWorkoutReminderTime(int id, DateTime scheduledDate, bool repeats) async {
    final prefs = await SharedPreferences.getInstance();
    final String? customTimesJson = prefs.getString(_customWorkoutTimesKey);
    List<Map<String, dynamic>> customTimes = [];
    if (customTimesJson != null) {
      customTimes = List<Map<String, dynamic>>.from(jsonDecode(customTimesJson));
    }
    customTimes.add({
      'id': id,
      'scheduledDate': scheduledDate.toIso8601String(),
      'repeats': repeats,
    });
    await prefs.setString(_customWorkoutTimesKey, jsonEncode(customTimes));
  }

  // Delete custom workout reminder time from Shared Preferences
  Future<void> _deleteCustomWorkoutReminderTime(int id) async {
    final prefs = await SharedPreferences.getInstance();
    final String? customTimesJson = prefs.getString(_customWorkoutTimesKey);
    if (customTimesJson != null) {
      List<Map<String, dynamic>> customTimes = List<Map<String, dynamic>>.from(jsonDecode(customTimesJson));
      customTimes.removeWhere((item) => item['id'] == id);
      await prefs.setString(_customWorkoutTimesKey, jsonEncode(customTimes));
    }
  }

  // ==================== GENERAL NOTIFICATION METHODS ====================

  /// Show a basic notification
  Future<void> showBasicNotification({
    required int id,
    required String title,
    required String body,
    String channelKey = generalChannelId,
  }) async {
    try {
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: id,
          channelKey: channelKey,
          title: title,
          body: body,
          notificationLayout: NotificationLayout.Default,
        ),
      );
      print('🔔 Basic notification $id shown');
    } catch (e) {
      print('❌ Error showing basic notification $id: $e');
    }
  }

  /// Show a scheduled notification
  Future<void> showScheduledNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String channelKey = generalChannelId,
    bool repeats = false,
  }) async {
    try {
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: id,
          channelKey: channelKey,
          title: title,
          body: body,
          notificationLayout: NotificationLayout.Default,
        ),
        schedule: NotificationCalendar(
          year: scheduledDate.year,
          month: scheduledDate.month,
          day: scheduledDate.day,
          hour: scheduledDate.hour,
          minute: scheduledDate.minute,
          second: scheduledDate.second,
          repeats: repeats,
          allowWhileIdle: true,
          preciseAlarm: true,
        ),
      );
      print('🔔 Scheduled notification $id for $scheduledDate');
    } catch (e) {
      print('❌ Error showing scheduled notification $id: $e');
    }
  }

  /// Check if a notification is scheduled
  Future<bool> isNotificationScheduled(int id) async {
    try {
      final list = await AwesomeNotifications().listScheduledNotifications();
      return list.any((n) => n.content?.id == id);
    } catch (e) {
      print('❌ Error checking if notification is scheduled: $e');
      return false;
    }
  }

  /// Check if workout reminder is currently scheduled and working
  Future<bool> isWorkoutReminderScheduled() async {
    try {
      return await isNotificationScheduled(workoutReminderId);
    } catch (e) {
      print('❌ Error checking workout reminder status: $e');
      return false;
    }
  }

  /// Get detailed information about scheduled notifications
  Future<List<Map<String, dynamic>>> getScheduledNotificationsInfo() async {
    try {
      final notifications = await AwesomeNotifications().listScheduledNotifications();
      return notifications.map((notification) {
        final content = notification.content;
        final schedule = notification.schedule;
        
        return {
          'id': content?.id,
          'title': content?.title,
          'body': content?.body,
          'channelKey': content?.channelKey,
          'scheduledDate': schedule?.toString(),
          'repeats': schedule?.repeats ?? false,
          'allowWhileIdle': schedule?.allowWhileIdle ?? false,
          'preciseAlarm': schedule?.preciseAlarm ?? false,
        };
      }).toList();
    } catch (e) {
      print('❌ Error getting scheduled notifications info: $e');
      return [];
    }
  }

  /// Reset all notification settings and cancel all reminders
  Future<void> resetAllNotificationSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_workoutNotificationsKey);
      await prefs.remove(_waterNotificationsKey);
      await prefs.remove(_workoutTimeKey);
      await prefs.remove(_waterIntervalKey);
      await prefs.remove(_customWorkoutTimesKey);
      await AwesomeNotifications().cancelAll();
      print('🔔 All notification settings reset and reminders cancelled.');
    } catch (e) {
      print('❌ Error resetting all notification settings: $e');
    }
  }

  /// Initialize notifications
  Future<void> initializeNotifications() async {
    await initialize();
  }

  /// Set workout reminder time
  Future<void> setWorkoutReminderTime(String timeString) async {
    try {
      TimeOfDay time;
      
      // Handle different time formats
      if (timeString.contains('AM') || timeString.contains('PM')) {
        // 12-hour format like "8:58 AM" or "2:30 PM"
        final parts = timeString.split(' ');
        final timePart = parts[0];
        final period = parts[1];
        
        final timeComponents = timePart.split(':');
        int hour = int.parse(timeComponents[0]);
        int minute = int.parse(timeComponents[1]);
        
        // Convert to 24-hour format
        if (period == 'PM' && hour != 12) {
          hour += 12;
        } else if (period == 'AM' && hour == 12) {
          hour = 0;
        }
        
        time = TimeOfDay(hour: hour, minute: minute);
      } else {
        // 24-hour format like "08:58"
        final parts = timeString.split(':');
        time = TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
      }
      
      await scheduleWorkoutReminders(time);
      print('🔔 Workout reminder time set to $timeString (${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')})');
    } catch (e) {
      print('❌ Error setting workout reminder time: $e');
      rethrow;
    }
  }

  /// Set custom workout times
  Future<void> setCustomWorkoutTimes(List<Map<String, dynamic>> times) async {
    try {
      // First, cancel all existing custom workout notifications
      await _cancelAllCustomWorkoutNotifications();
      
      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_customWorkoutTimesKey, jsonEncode(times));
      
      // Schedule notifications for enabled custom workout times
      for (final workoutTime in times) {
        if (workoutTime['enabled'] == true) {
          await _scheduleCustomWorkoutNotification(workoutTime);
        }
      }
      
      print('🔔 Custom workout times updated and notifications scheduled');
    } catch (e) {
      print('❌ Error setting custom workout times: $e');
      rethrow;
    }
  }

  /// Schedule a single custom workout notification
  Future<void> _scheduleCustomWorkoutNotification(Map<String, dynamic> workoutTime) async {
    try {
      final timeString = workoutTime['time'] as String;
      final type = workoutTime['type'] as String;
      final days = List<int>.from(workoutTime['days'] as List);
      final enabled = workoutTime['enabled'] as bool? ?? true;
      
      if (!enabled) return;
      
      // Parse time
      final timeParts = timeString.split(':');
      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);
      
      print('🔔 Scheduling custom workout: $type at ${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} on days: $days');
      
      // Generate unique ID for this custom workout
      final baseId = customWorkoutReminderId + timeString.hashCode + type.hashCode;
      
      // Schedule for each selected day
      for (final day in days) {
        final now = DateTime.now();
        
        // Calculate the next occurrence of this day
        var scheduledDate = _getNextOccurrenceOfDay(day, hour, minute);
        
        // Generate unique ID for this specific day
        final notificationId = baseId + day;
        
        await AwesomeNotifications().createNotification(
          content: NotificationContent(
            id: notificationId,
            channelKey: workoutChannelId,
            title: '💪 $type Time!',
            body: 'Time for your $type workout. Let\'s get moving!',
            notificationLayout: NotificationLayout.Default,
            autoDismissible: true,
            wakeUpScreen: true,
            category: NotificationCategory.Reminder,
            criticalAlert: true,
          ),
          schedule: NotificationCalendar(
            year: scheduledDate.year,
            month: scheduledDate.month,
            day: scheduledDate.day,
            hour: scheduledDate.hour,
            minute: scheduledDate.minute,
            second: 0,
            repeats: true,
            allowWhileIdle: true,
            preciseAlarm: true,
          ),
        );
        
        print('🔔 Custom workout notification scheduled: $type on day $day at ${scheduledDate.toString()} (ID: $notificationId)');
      }
    } catch (e) {
      print('❌ Error scheduling custom workout notification: $e');
      rethrow;
    }
  }

  /// Get the next occurrence of a specific day of the week at the given time
  DateTime _getNextOccurrenceOfDay(int targetDay, int hour, int minute) {
    final now = DateTime.now();
    final currentDayOfWeek = now.weekday; // 1=Monday, 7=Sunday
    
    // Calculate days to add
    int daysToAdd = targetDay - currentDayOfWeek;
    if (daysToAdd <= 0) {
      daysToAdd += 7; // Next week
    }
    
    var scheduledDate = DateTime(
      now.year,
      now.month,
      now.day + daysToAdd,
      hour,
      minute,
    );
    
    // If the calculated time is still in the past, add another week
    if (scheduledDate.isBefore(now)) {
      scheduledDate = scheduledDate.add(const Duration(days: 7));
    }
    
    return scheduledDate;
  }

  /// Cancel all custom workout notifications
  Future<void> _cancelAllCustomWorkoutNotifications() async {
    try {
      final customTimes = await getCustomWorkoutTimes();
      for (final workoutTime in customTimes) {
        final timeString = workoutTime['time'] as String;
        final type = workoutTime['type'] as String;
        final days = List<int>.from(workoutTime['days'] as List);
        
        // Generate unique ID for this custom workout (same logic as scheduling)
        final baseId = customWorkoutReminderId + timeString.hashCode + type.hashCode;
        
        for (final day in days) {
          final notificationId = baseId + day;
          await AwesomeNotifications().cancel(notificationId);
          print('🔔 Cancelled custom workout notification ID: $notificationId');
        }
      }
      print('🔔 All custom workout notifications cancelled');
    } catch (e) {
      print('❌ Error cancelling custom workout notifications: $e');
    }
  }

  /// Send 16 second test notification
  Future<void> send16SecondTest() async {
    try {
      await showScheduledNotification(
        id: testNotificationId,
        title: '16 Second Test',
        body: 'This notification was scheduled for 16 seconds!',
        scheduledDate: DateTime.now().add(const Duration(seconds: 16)),
      );
      print('🔔 16 second test notification scheduled');
    } catch (e) {
      print('❌ Error sending 16 second test: $e');
      rethrow;
    }
  }

  /// Send 17 second test notification
  Future<void> send17SecondTest() async {
    try {
      await showScheduledNotification(
        id: testNotificationId + 1,
        title: '17 Second Test',
        body: 'This notification was scheduled for 17 seconds!',
        scheduledDate: DateTime.now().add(const Duration(seconds: 17)),
      );
      print('🔔 17 second test notification scheduled');
    } catch (e) {
      print('❌ Error sending 17 second test: $e');
      rethrow;
    }
  }

  /// Send 21 second test notification
  Future<void> send21SecondTest() async {
    try {
      await showScheduledNotification(
        id: testNotificationId + 2,
        title: '21 Second Test',
        body: 'This notification was scheduled for 21 seconds!',
        scheduledDate: DateTime.now().add(const Duration(seconds: 21)),
      );
      print('🔔 21 second test notification scheduled');
    } catch (e) {
      print('❌ Error sending 21 second test: $e');
      rethrow;
    }
  }

  /// Schedule workout reminder
  Future<void> scheduleWorkoutReminder(TimeOfDay time) async {
    try {
      final now = DateTime.now();
      var scheduledDate = DateTime(
        now.year,
        now.month,
        now.day,
        time.hour,
        time.minute,
      );

      if (scheduledDate.isBefore(now)) {
        scheduledDate = scheduledDate.add(const Duration(days: 1));
      }

      await showScheduledNotification(
        id: workoutReminderId,
        title: 'Time to Work Out! 💪',
        body: 'Your scheduled workout time has arrived. Let\'s get moving!',
        scheduledDate: scheduledDate,
        channelKey: workoutChannelId,
        repeats: true,
      );

      await _saveWorkoutReminderTime(time);
      print('🔔 Workout reminder scheduled for ${time.hour}:${time.minute}');
    } catch (e) {
      print('❌ Error scheduling workout reminder: $e');
      rethrow;
    }
  }

  /// Get workout reminder system summary
  Future<Map<String, dynamic>> getWorkoutReminderSummary() async {
    try {
      final workoutEnabled = await isWorkoutNotificationEnabled();
      final workoutTime = await getWorkoutReminderTime();
      final workoutScheduled = await isWorkoutReminderScheduled();
      final hasPerms = await hasPermissions();
      final scheduledNotifications = await getScheduledNotificationsInfo();
      
      return {
        'enabled': workoutEnabled,
        'time': workoutTime != null ? '${workoutTime.hour.toString().padLeft(2, '0')}:${workoutTime.minute.toString().padLeft(2, '0')}' : 'Not set',
        'scheduled': workoutScheduled,
        'permissions': hasPerms,
        'totalScheduled': scheduledNotifications.length,
        'workoutNotifications': scheduledNotifications.where((n) => n['channelKey'] == workoutChannelId).length,
        'systemStatus': workoutEnabled && workoutScheduled && hasPerms ? 'Working' : 'Not Working',
      };
    } catch (e) {
      print('❌ Error getting workout reminder summary: $e');
      return {
        'enabled': false,
        'time': 'Error',
        'scheduled': false,
        'permissions': false,
        'totalScheduled': 0,
        'workoutNotifications': 0,
        'systemStatus': 'Error',
      };
    }
  }

  /// Test custom workout notification for today (always schedules for 10 seconds from now)
  Future<void> testCustomWorkoutNotificationToday(Map<String, dynamic> workoutTime) async {
    try {
      final type = workoutTime['type'] as String;
      final now = DateTime.now();
      final testScheduledTime = now.add(const Duration(seconds: 10));
      final testId = customWorkoutReminderId + 999999 + type.hashCode;
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: testId,
          channelKey: workoutChannelId,
          title: '🧪 Test: $type Workout',
          body: 'This is a test notification for your $type workout scheduled for today!',
          notificationLayout: NotificationLayout.Default,
          autoDismissible: true,
          wakeUpScreen: true,
          category: NotificationCategory.Reminder,
          criticalAlert: true,
        ),
        schedule: NotificationCalendar(
          year: testScheduledTime.year,
          month: testScheduledTime.month,
          day: testScheduledTime.day,
          hour: testScheduledTime.hour,
          minute: testScheduledTime.minute,
          second: testScheduledTime.second,
          repeats: false,
          allowWhileIdle: true,
          preciseAlarm: true,
        ),
      );
      print('🧪 Custom workout test notification scheduled: $type at ${testScheduledTime.toString()} (ID: $testId)');
    } catch (e) {
      print('❌ Error scheduling custom workout test notification: $e');
      rethrow;
    }
  }

  /// Check if custom workout notifications are scheduled
  Future<bool> areCustomWorkoutNotificationsScheduled() async {
    try {
      final customTimes = await getCustomWorkoutTimes();
      if (customTimes.isEmpty) return false;
      
      final scheduledNotifications = await AwesomeNotifications().listScheduledNotifications();
      final customWorkoutNotifications = scheduledNotifications.where((notification) {
        final content = notification.content;
        return content != null && 
               content.channelKey == workoutChannelId &&
               content.title != null &&
               content.title!.contains('💪');
      }).toList();
      
      print('🔍 Found ${customWorkoutNotifications.length} custom workout notifications scheduled');
      return customWorkoutNotifications.isNotEmpty;
    } catch (e) {
      print('❌ Error checking custom workout notifications: $e');
      return false;
    }
  }
}
