// This file is deprecated. Use SimpleVimeoPlayer for all Vimeo playback.

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:vimeo_video_player/vimeo_video_player.dart';
import '../models/course_video.dart';
import '../services/api_service.dart';
import '../services/progress_service.dart';
import '../utils/video_security_helper.dart';

class EnhancedVimeoPlayer extends StatefulWidget {
  final CourseVideo video;
  final int? courseId;
  final Function(int)? onProgress;
  final Function()? onCompleted;
  final Function(String)? onError;
  final Function()? onPlay;
  final Function()? onPause;
  final Function()? onReady;
  final bool autoPlay;
  final bool showControls;
  final bool enablePictureInPicture;
  final bool enableFullscreen;
  final List<double> playbackSpeeds;
  final double initialPlaybackSpeed;
  final bool enableQualitySelection;
  final bool enableSubtitles;
  final bool showProgressIndicator;
  final bool enableGestures;

  const EnhancedVimeoPlayer({
    Key? key,
    required this.video,
    this.courseId,
    this.onProgress,
    this.onCompleted,
    this.onError,
    this.onPlay,
    this.onPause,
    this.onReady,
    this.autoPlay = false,
    this.showControls = true,
    this.enablePictureInPicture = true,
    this.enableFullscreen = true,
    this.playbackSpeeds = const [0.5, 0.75, 1.0, 1.25, 1.5, 2.0],
    this.initialPlaybackSpeed = 1.0,
    this.enableQualitySelection = true,
    this.enableSubtitles = false,
    this.showProgressIndicator = true,
    this.enableGestures = true,
  }) : super(key: key);

  @override
  State<EnhancedVimeoPlayer> createState() => _EnhancedVimeoPlayerState();
}

class _EnhancedVimeoPlayerState extends State<EnhancedVimeoPlayer>
    with TickerProviderStateMixin {
  WebViewController? _webViewController;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  int _currentPosition = 0;
  int _duration = 0;
  bool _isPlaying = false;
  bool _isCompleted = false;
  bool _showControls = true;
  double _currentPlaybackSpeed = 1.0;
  String _currentQuality = 'auto';
  bool _isBuffering = false;
  bool _isFullscreen = false;

  // Animation controllers
  late AnimationController _controlsAnimationController;
  late AnimationController _loadingAnimationController;
  late Animation<double> _controlsOpacity;
  late Animation<double> _loadingRotation;

  // Security and tracking
  final ApiService _apiService = ApiService();
  String? _secureEmbedUrl;
  bool _isSecurityValidated = false;

  // Progress tracking
  int _lastProgressUpdate = 0;
  static const int _progressUpdateInterval = 5; // seconds

  @override
  void initState() {
    super.initState();
    _currentPlaybackSpeed = widget.initialPlaybackSpeed;
    _initializeAnimations();
    _initializePlayer();
  }

  void _initializeAnimations() {
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _loadingAnimationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _controlsOpacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controlsAnimationController,
      curve: Curves.easeInOut,
    ));

    _loadingRotation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.linear,
    ));

    if (widget.showControls) {
      _controlsAnimationController.forward();
    }
  }

  @override
  void dispose() {
    _controlsAnimationController.dispose();
    _loadingAnimationController.dispose();
    super.dispose();
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // Validate video security and get secure embed URL
      await _validateVideoSecurity();

      if (_isSecurityValidated && _secureEmbedUrl != null) {
        await _setupVimeoPlayer();
        widget.onReady?.call();
      } else {
        _handleError('Video access denied or security validation failed');
      }
    } catch (e) {
      _handleError('Failed to initialize video player: $e');
    }
  }

  Future<void> _validateVideoSecurity() async {
    try {
      // Extract Vimeo ID from the video URL
      final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);

      if (vimeoId == null) {
        throw Exception('Invalid Vimeo video ID');
      }

      // Validate domain and get secure embed URL
      _secureEmbedUrl = await VideoSecurityHelper.getSecureEmbedUrl(
        vimeoId: vimeoId,
        videoId: widget.video.id,
        userId: await _getCurrentUserId(),
      );

      if (_secureEmbedUrl != null) {
        _isSecurityValidated = true;

        // Log video access
        await VideoSecurityHelper.logVideoAccess(
          vimeoId: vimeoId,
          videoId: widget.video.id,
          userId: await _getCurrentUserId(),
          action: 'access',
        );
      }
    } catch (e) {
      debugPrint('Security validation failed: $e');
      _isSecurityValidated = false;
    }
  }

  Future<int> _getCurrentUserId() async {
    try {
      final response = await _apiService.makeApiRequest('profile.php');
      if (response['success'] == true && response['profile'] != null) {
        return response['profile']['id'] ?? 1;
      }
      return 1; // Fallback
    } catch (e) {
      debugPrint('Failed to get current user ID: $e');
      return 1; // Fallback
    }
  }

  Future<void> _setupVimeoPlayer() async {
    try {
      if (kIsWeb) {
        await _setupWebPlayer();
      } else {
        await _setupMobilePlayer();
      }
    } catch (e) {
      _handleError('Failed to setup player: $e');
    }
  }

  Future<void> _setupWebPlayer() async {
    // Enhanced web player setup with better error handling and inline playback
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
              _isBuffering = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
              _isBuffering = false;
            });
            _setupWebPlayerListeners();
          },
          onWebResourceError: (WebResourceError error) {
            _handleError('Web player error: ${error.description}');
          },
        ),
      )
      // Enable inline media playback and gesture permissions for Vimeo seeking
      ..enableZoom(false);

    // Load the enhanced secure Vimeo embed
    final embedHtml = _buildEnhancedEmbedHtml();
    await _webViewController!.loadHtmlString(embedHtml);
  }

  Future<void> _setupMobilePlayer() async {
    try {
      final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);

      if (vimeoId == null) {
        throw Exception('Invalid Vimeo ID');
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      _handleError('Failed to setup mobile player: $e');
    }
  }

  void _setupWebPlayerListeners() {
    // Enhanced JavaScript integration with better event handling
    if (_webViewController == null) return;

    _webViewController!.runJavaScript('''
      var iframe = document.querySelector('iframe');
      var player = new Vimeo.Player(iframe);

      // Enhanced event listeners
      player.on('play', function() {
        window.flutter_inappwebview.callHandler('onPlay');
      });

      player.on('pause', function() {
        window.flutter_inappwebview.callHandler('onPause');
      });

      player.on('timeupdate', function(data) {
        window.flutter_inappwebview.callHandler('onTimeUpdate', data.seconds);
      });

      player.on('ended', function() {
        window.flutter_inappwebview.callHandler('onEnded');
      });

      player.on('bufferstart', function() {
        window.flutter_inappwebview.callHandler('onBufferStart');
      });

      player.on('bufferend', function() {
        window.flutter_inappwebview.callHandler('onBufferEnd');
      });

      player.on('error', function(error) {
        window.flutter_inappwebview.callHandler('onError', error.message);
      });

      // Get video metadata
      player.getDuration().then(function(duration) {
        window.flutter_inappwebview.callHandler('onDuration', duration);
      });

      player.getVideoQuality().then(function(quality) {
        window.flutter_inappwebview.callHandler('onQuality', quality);
      });
    ''');
  }

  String _buildEnhancedEmbedHtml() {
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {
                margin: 0;
                padding: 0;
                background: #000;
                overflow: hidden;
            }
            iframe {
                width: 100%;
                height: 100vh;
                border: none;
                display: block;
            }
            .loading {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: white;
                font-family: Arial, sans-serif;
            }
        </style>
        <script src="https://player.vimeo.com/api/player.js"></script>
    </head>
    <body>
        <div class="loading" id="loading">Loading...</div>
        <iframe src="$_secureEmbedUrl"
                frameborder="0"
                allow="autoplay; fullscreen; picture-in-picture; accelerometer; gyroscope; microphone; camera; encrypted-media; web-share"
                allowfullscreen
                webkitPlaysinline="true"
                playsinline="true"
                referrerpolicy="origin"
                onload="document.getElementById('loading').style.display='none'">
        </iframe>
    </body>
    </html>
    ''';
  }

  // Enhanced event handlers
  void _onVimeoPlay() {
    setState(() {
      _isPlaying = true;
      _isBuffering = false;
    });

    widget.onPlay?.call();
    _trackVideoProgress();
    _logVideoEvent('play');
  }

  void _onVimeoPause() {
    setState(() {
      _isPlaying = false;
    });

    widget.onPause?.call();
    _logVideoEvent('pause');
  }

  void _onVimeoTimeUpdate(int position) {
    setState(() {
      _currentPosition = position;
    });

    widget.onProgress?.call(position);

    // Update progress every 5 seconds
    if (position - _lastProgressUpdate >= _progressUpdateInterval) {
      _updateVideoProgress();
      _lastProgressUpdate = position;
    }

    // Check for completion (80% watched)
    if (_duration > 0 && position >= (_duration * 0.8) && !_isCompleted) {
      _onVideoCompleted();
    }
  }

  void _onVimeoFinish() {
    _onVideoCompleted();
  }

  void _onVideoCompleted() {
    setState(() {
      _isCompleted = true;
      _isPlaying = false;
    });

    widget.onCompleted?.call();
    _updateVideoProgress(isCompleted: true);
    _logVideoEvent('complete');
  }

  void _onBufferStart() {
    setState(() {
      _isBuffering = true;
    });
  }

  void _onBufferEnd() {
    setState(() {
      _isBuffering = false;
    });
  }

  Future<void> _trackVideoProgress() async {
    // Enhanced progress tracking with better intervals
    while (_isPlaying && mounted) {
      await Future.delayed(const Duration(seconds: 5));
      if (_isPlaying && mounted) {
        await _updateVideoProgress();
      }
    }
  }

  Future<void> _updateVideoProgress({bool isCompleted = false}) async {
    try {
      final progressService = ProgressService();
      await progressService.updateVideoProgress(
        videoId: widget.video.id,
        courseId: widget.courseId,
        watchDurationSeconds: _currentPosition,
        lastPositionSeconds: _currentPosition,
        isCompleted: isCompleted,
        totalDurationSeconds: _duration,
      );
      debugPrint('✅ Video progress updated - Position: $_currentPosition, Completed: $isCompleted');
    } catch (e) {
      debugPrint('❌ Failed to update video progress: $e');
      // Don't rethrow to avoid disrupting playback
    }
  }

  Future<void> _logVideoEvent(String action) async {
    try {
      final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);
      if (vimeoId != null) {
        await VideoSecurityHelper.logVideoAccess(
          vimeoId: vimeoId,
          videoId: widget.video.id,
          userId: await _getCurrentUserId(),
          action: action,
        );
      }
    } catch (e) {
      // Do NOT clear tokens or logout on video event logging errors
      // Video event logging is for analytics only and should not affect authentication
      debugPrint('Failed to log video event: $e');
    }
  }

  void _handleError(String message) {
    setState(() {
      _hasError = true;
      _errorMessage = message;
      _isLoading = false;
      _isBuffering = false;
    });
    widget.onError?.call(message);
  }

  void _togglePlayPause() {
    if (_isPlaying) {
      _pauseVideo();
    } else {
      _playVideo();
    }
  }

  void _playVideo() {
    if (kIsWeb && _webViewController != null) {
      _webViewController!.runJavaScript('player.play()');
    }
    // For mobile, the VimeoVideoPlayer handles this internally
  }

  void _pauseVideo() {
    if (kIsWeb && _webViewController != null) {
      _webViewController!.runJavaScript('player.pause()');
    }
    // For mobile, the VimeoVideoPlayer handles this internally
  }

  void _seekTo(int seconds) {
    if (kIsWeb && _webViewController != null) {
      _webViewController!.runJavaScript('player.setCurrentTime($seconds)');
    }
    // For mobile, seeking would need to be implemented differently
  }

  void _changePlaybackSpeed(double speed) {
    setState(() {
      _currentPlaybackSpeed = speed;
    });

    if (kIsWeb && _webViewController != null) {
      _webViewController!.runJavaScript('player.setPlaybackRate($speed)');
    }
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });

    // Note: Orientation changes are handled by Vimeo player's native fullscreen
    debugPrint('EnhancedVimeoPlayer: Fullscreen toggled - $_isFullscreen');
  }

  void _showControlsTemporarily() {
    if (widget.showControls) {
      setState(() {
        _showControls = true;
      });
      _controlsAnimationController.forward();

      // Hide controls after 3 seconds
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted && _isPlaying) {
          setState(() {
            _showControls = false;
          });
          _controlsAnimationController.reverse();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      return _buildErrorWidget();
    }

    if (_isLoading) {
      return _buildLoadingWidget();
    }

    return GestureDetector(
      onTap: widget.enableGestures ? _showControlsTemporarily : null,
      child: Stack(
        children: [
          // Video player
          _buildPlayerWidget(),

          // Buffering indicator
          if (_isBuffering) _buildBufferingIndicator(),

          // Custom controls overlay
          if (widget.showControls && _showControls)
            _buildControlsOverlay(),

          // Progress indicator
          if (widget.showProgressIndicator && !_showControls)
            _buildProgressIndicator(),
        ],
      ),
    );
  }

  Widget _buildPlayerWidget() {
    if (kIsWeb) {
      // Only show WebView if controller is initialized
      if (_webViewController != null) {
        return WebViewWidget(
          controller: _webViewController!,
          gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
            Factory<TapGestureRecognizer>(() => TapGestureRecognizer()),
            Factory<LongPressGestureRecognizer>(() => LongPressGestureRecognizer()),
            Factory<VerticalDragGestureRecognizer>(() => VerticalDragGestureRecognizer()),
            Factory<HorizontalDragGestureRecognizer>(() => HorizontalDragGestureRecognizer()),
          },
        );
      } else {
        // Show loading indicator if WebViewController is not ready
        return Container(
          color: Colors.black,
          child: const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
        );
      }
    } else {
      final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);

      if (vimeoId != null) {
        return VimeoVideoPlayer(
          videoId: vimeoId,
          isAutoPlay: widget.autoPlay,
          showControls: true, // Use Vimeo's default controls
          showTitle: false,
          showByline: false,
          isMuted: false,
          isLooping: false,
          enableDNT: true,
          backgroundColor: Colors.black,
          onReady: () {
            debugPrint('Enhanced Vimeo player ready');
            widget.onReady?.call();
          },
          onPlay: _onVimeoPlay,
          onPause: _onVimeoPause,
          onFinish: _onVimeoFinish,
        );
      } else {
        return _buildErrorWidget();
      }
    }
  }

  Widget _buildErrorWidget() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              'Video Unavailable',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white70,
                ),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _hasError = false;
                  _isLoading = true;
                });
                _initializePlayer();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            RotationTransition(
              turns: _loadingRotation,
              child: const Icon(
                Icons.refresh,
                color: Colors.white,
                size: 48,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Loading video...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            if (widget.video.title.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                widget.video.title,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBufferingIndicator() {
    return Container(
      color: Colors.black54,
      child: const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
          strokeWidth: 3,
        ),
      ),
    );
  }

  Widget _buildControlsOverlay() {
    return AnimatedBuilder(
      animation: _controlsOpacity,
      builder: (context, child) {
        return Opacity(
          opacity: _controlsOpacity.value,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.7),
                  Colors.transparent,
                  Colors.black.withOpacity(0.7),
                ],
                stops: const [0.0, 0.5, 1.0],
              ),
            ),
            child: Column(
              children: [
                // Top controls
                _buildTopControls(),

                // Center play/pause button
                Expanded(
                  child: Center(
                    child: _buildCenterControls(),
                  ),
                ),

                // Bottom controls
                _buildBottomControls(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopControls() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Faded minimal back button
            Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Icon(
                    Icons.arrow_back,
                    color: Colors.white.withOpacity(0.7),
                    size: 24,
                  ),
                ),
              ),
            ),

            // Video title
            Expanded(
              child: Text(
                widget.video.title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // Settings button
            if (widget.enableQualitySelection || widget.playbackSpeeds.length > 1)
              IconButton(
                onPressed: _showSettingsMenu,
                icon: const Icon(Icons.settings, color: Colors.white),
              ),

            // Fullscreen button
            if (widget.enableFullscreen)
              IconButton(
                onPressed: _toggleFullscreen,
                icon: Icon(
                  _isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
                  color: Colors.white,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCenterControls() {
    return GestureDetector(
      onTap: _togglePlayPause,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          shape: BoxShape.circle,
        ),
        child: Icon(
          _isPlaying ? Icons.pause : Icons.play_arrow,
          color: Colors.white,
          size: 40,
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Progress bar
            _buildProgressBar(),

            const SizedBox(height: 12),

            // Control buttons
            Row(
              children: [
                // Current time
                Text(
                  _formatDuration(_currentPosition),
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                ),

                const Spacer(),

                // Playback speed
                if (widget.playbackSpeeds.length > 1)
                  TextButton(
                    onPressed: _showSpeedMenu,
                    child: Text(
                      '${_currentPlaybackSpeed}x',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),

                const Spacer(),

                // Total duration
                Text(
                  _formatDuration(_duration),
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressBar() {
    return SliderTheme(
      data: SliderTheme.of(context).copyWith(
        activeTrackColor: Colors.red,
        inactiveTrackColor: Colors.white.withOpacity(0.3),
        thumbColor: Colors.red,
        thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
        overlayShape: const RoundSliderOverlayShape(overlayRadius: 12),
      ),
      child: Slider(
        value: _duration > 0 ? _currentPosition.toDouble() : 0.0,
        max: _duration.toDouble(),
        onChanged: (value) {
          _seekTo(value.toInt());
        },
      ),
    );
  }

  Widget _buildProgressIndicator() {
    if (_duration <= 0) return const SizedBox.shrink();

    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: LinearProgressIndicator(
        value: _currentPosition / _duration,
        backgroundColor: Colors.white.withOpacity(0.3),
        valueColor: const AlwaysStoppedAnimation<Color>(Colors.red),
        minHeight: 2,
      ),
    );
  }

  void _showSettingsMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black87,
      builder: (context) => _buildSettingsMenu(),
    );
  }

  Widget _buildSettingsMenu() {
    return SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              'Video Settings',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          if (widget.playbackSpeeds.length > 1) ...[
            const Divider(color: Colors.white24),
            _buildSpeedSettings(),
          ],

          if (widget.enableQualitySelection) ...[
            const Divider(color: Colors.white24),
            _buildQualitySettings(),
          ],

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildSpeedSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Playback Speed',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.w500),
          ),
        ),
        ...widget.playbackSpeeds.map((speed) => ListTile(
          title: Text(
            '${speed}x',
            style: const TextStyle(color: Colors.white),
          ),
          trailing: _currentPlaybackSpeed == speed
              ? const Icon(Icons.check, color: Colors.red)
              : null,
          onTap: () {
            _changePlaybackSpeed(speed);
            Navigator.pop(context);
          },
        )),
      ],
    );
  }

  Widget _buildQualitySettings() {
    final qualities = ['Auto', '1080p', '720p', '480p', '360p'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Video Quality',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.w500),
          ),
        ),
        ...qualities.map((quality) => ListTile(
          title: Text(
            quality,
            style: const TextStyle(color: Colors.white),
          ),
          trailing: _currentQuality.toLowerCase() == quality.toLowerCase()
              ? const Icon(Icons.check, color: Colors.red)
              : null,
          onTap: () {
            setState(() {
              _currentQuality = quality.toLowerCase();
            });
            Navigator.pop(context);
          },
        )),
      ],
    );
  }

  void _showSpeedMenu() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black87,
        title: const Text(
          'Playback Speed',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: widget.playbackSpeeds.map((speed) => ListTile(
            title: Text(
              '${speed}x',
              style: const TextStyle(color: Colors.white),
            ),
            trailing: _currentPlaybackSpeed == speed
                ? const Icon(Icons.check, color: Colors.red)
                : null,
            onTap: () {
              _changePlaybackSpeed(speed);
              Navigator.pop(context);
            },
          )).toList(),
        ),
      ),
    );
  }

  String _formatDuration(int seconds) {
    final duration = Duration(seconds: seconds);
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final secs = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    }
  }
}
