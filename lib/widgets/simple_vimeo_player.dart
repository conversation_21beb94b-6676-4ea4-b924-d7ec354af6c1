import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../models/course_video.dart';
import '../utils/video_security_helper.dart';
import '../services/api_service.dart';
import '../services/vimeo_pro_auth_service.dart';
import '../services/hosted_player_service.dart';
import '../services/robust_seek_service.dart';
import '../services/progress_service.dart';
import 'dart:convert';

/// Domain-Restricted Vimeo Player - Handles Vimeo domain restrictions automatically
/// Uses proper referrer headers and domain verification for seamless playback
class SimpleVimeoPlayer extends StatefulWidget {
  final CourseVideo video;
  final bool autoPlay;
  final VoidCallback? onReady;
  final VoidCallback? onPlay;
  final VoidCallback? onPause;
  final VoidCallback? onCompleted;
  final Function(int position)? onProgress;
  final Function(String error)? onError;
  final VoidCallback? onEnterFullscreen;
  final VoidCallback? onExitFullscreen;

  const SimpleVimeoPlayer({
    Key? key,
    required this.video,
    this.autoPlay = true,
    this.onReady,
    this.onPlay,
    this.onPause,
    this.onCompleted,
    this.onProgress,
    this.onError,
    this.onEnterFullscreen,
    this.onExitFullscreen,
  }) : super(key: key);

  @override
  State<SimpleVimeoPlayer> createState() => SimpleVimeoPlayerState();
}

class SimpleVimeoPlayerState extends State<SimpleVimeoPlayer> with SingleTickerProviderStateMixin {
  WebViewController? _preloadController;
  bool _showPlayer = false;
  bool _isPreloaded = false;
  late final String _playerUrl;
  bool _isFullscreen = false;

  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  late WebViewController _webViewController;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 350),
    );
    _scaleAnimation = Tween<double>(begin: 0.97, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );
    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    _animationController.value = 1.0;

    // Initialize player after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializePlayer();
    });
  }

  @override
  void dispose() {
    // Restore overlays and orientation for extra reliability
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    _animationController.dispose();
    super.dispose();
  }

  void resetAndPlay() {
    if (_preloadController != null && !_hasError) {
      _preloadController!.runJavaScript('''
        if (window.vimeoPlayer) {
          window.vimeoPlayer.loadVideo('${widget.video.videoUrl}').then(function() {
            window.vimeoPlayer.play();
          });
        }
      ''');
    }
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);
      if (vimeoId == null) {
        throw Exception('Invalid Vimeo video URL: ${widget.video.videoUrl}');
      }

      _playerUrl = 'https://mycloudforge.com/kft_vimeo_player.html?vimeo_id=$vimeoId&domain=mycloudforge.com&autoplay=1';

      _preloadController = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(NavigationDelegate(
          onPageFinished: (_) {
            Future.delayed(const Duration(seconds: 2), () {
              if (mounted) {
                setState(() {
                  _isPreloaded = true;
                  _isLoading = false;
                });
              }
            });
          },
          onWebResourceError: (error) {
            debugPrint('SimpleVimeoPlayer: WebView error: ${error.description}');
            if (mounted) {
              setState(() {
                _hasError = true;
                _errorMessage = 'Failed to load video: ${error.description}';
                _isLoading = false;
              });
            }
          },
        ));

      await _preloadController!.loadRequest(Uri.parse(_playerUrl));
    } catch (e) {
      debugPrint('SimpleVimeoPlayer: Initialization error: $e');
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = 'Failed to initialize video player: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _toggleFullscreen() async {
    if (_isFullscreen) {
      // Animate out
      await _animationController.reverse();
      // Exit fullscreen: portrait + show overlays
      await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
      if (mounted) setState(() => _isFullscreen = false);
      await _animationController.forward();
      widget.onExitFullscreen?.call();
    } else {
      // Animate in
      await _animationController.reverse();
      // Enter fullscreen: landscape + hide overlays
      _forceImmersiveMode();
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
      // Extra reliability: re-apply immersive mode after a short delay
      Future.delayed(const Duration(milliseconds: 300), () {
        _forceImmersiveMode();
      });
      if (mounted) setState(() => _isFullscreen = true);
      await _animationController.forward();
      widget.onEnterFullscreen?.call();
    }
  }

  void _forceImmersiveMode() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    Future.delayed(const Duration(milliseconds: 300), () {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    });
  }

  Future<bool> handleBackPressed() async {
    if (_isFullscreen) {
      await _toggleFullscreen();
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Material(
          color: Colors.black.withOpacity(0.9 * _opacityAnimation.value),
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: child,
          ),
        );
      },
      child: Stack(
        children: [
          // Show WebViewWidget only when controller is ready
          if (_preloadController != null && !_hasError)
            WebViewWidget(controller: _preloadController!),

          // Show loading indicator
          if (_isLoading)
            Container(
              color: Colors.black,
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),

          // Show error message
          if (_hasError)
            Container(
              color: Colors.black,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.white,
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Video Error',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32),
                      child: Text(
                        _errorMessage,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _hasError = false;
                          _errorMessage = '';
                        });
                        _initializePlayer();
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),

          // Play button overlay
          if (!_showPlayer && !_isLoading && !_hasError)
            Positioned.fill(
              child: Container(
                color: Colors.black.withOpacity(0.3),
                child: Center(
                  child: GestureDetector(
                    onTap: () => setState(() => _showPlayer = true),
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.6),
                        shape: BoxShape.circle,
                      ),
                      padding: const EdgeInsets.all(24),
                      child: const Icon(Icons.play_circle, size: 80, color: Colors.white),
                    ),
                  ),
                ),
              ),
            ),

          // Fullscreen toggle button (top right, small, clean, no bg)
          if (_showPlayer && !_isLoading && !_hasError)
            Positioned(
              top: 12,
              right: 12,
              child: GestureDetector(
                onTap: _toggleFullscreen,
                child: Icon(
                  _isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
                  size: 28,
                  color: Colors.white,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Add this method for compatibility with orientation/state restoration
  void restoreVideoPosition() {
    // No-op for now (preload player does not support position restore yet)
  }
}
