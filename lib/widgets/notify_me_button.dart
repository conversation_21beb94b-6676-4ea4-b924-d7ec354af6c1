import 'package:flutter/material.dart';
import '../services/workout_video_notification_service.dart';
import '../models/course_video.dart';

/// A button widget for locked videos that allows users to request notifications
/// when the video becomes unlocked
class NotifyMeButton extends StatefulWidget {
  final CourseVideo video;
  final VoidCallback? onNotificationToggled;

  const NotifyMeButton({
    Key? key,
    required this.video,
    this.onNotificationToggled,
  }) : super(key: key);

  @override
  State<NotifyMeButton> createState() => _NotifyMeButtonState();
}

class _NotifyMeButtonState extends State<NotifyMeButton> {
  final WorkoutVideoNotificationService _notificationService = WorkoutVideoNotificationService();
  bool _isInNotifyList = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkNotifyStatus();
  }

  Future<void> _checkNotifyStatus() async {
    try {
      final isInList = await _notificationService.isVideoInNotifyMeList(widget.video.id);
      if (mounted) {
        setState(() {
          _isInNotifyList = isInList;
        });
      }
    } catch (e) {
      print('Error checking notify status: $e');
    }
  }

  Future<void> _toggleNotifyMe() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isInNotifyList) {
        // Remove from notify list
        await _notificationService.removeNotifyMeVideo(widget.video.id);
        setState(() {
          _isInNotifyList = false;
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.notifications_off, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text('Notification removed for "${widget.video.title}"'),
                  ),
                ],
              ),
              backgroundColor: Colors.orange,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } else {
        // Add to notify list
        await _notificationService.addNotifyMeVideo(widget.video.id, widget.video.title);
        setState(() {
          _isInNotifyList = true;
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.notifications_active, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text('You\'ll be notified when "${widget.video.title}" unlocks!'),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }

      // Call callback if provided
      widget.onNotificationToggled?.call();
    } catch (e) {
      print('Error toggling notify me: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.only(top: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _isLoading ? null : _toggleNotifyMe,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: _isInNotifyList 
                ? Colors.green.withOpacity(0.1)
                : theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _isInNotifyList 
                  ? Colors.green.withOpacity(0.3)
                  : theme.colorScheme.primary.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_isLoading) ...[
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        _isInNotifyList ? Colors.green : theme.colorScheme.primary,
                      ),
                    ),
                  ),
                ] else ...[
                  Icon(
                    _isInNotifyList ? Icons.notifications_active : Icons.notifications_none,
                    size: 18,
                    color: _isInNotifyList ? Colors.green : theme.colorScheme.primary,
                  ),
                ],
                const SizedBox(width: 8),
                Text(
                  _isInNotifyList ? 'Notification Set' : 'Notify Me',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: _isInNotifyList ? Colors.green : theme.colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// A compact version of the notify me button for use in video cards
class CompactNotifyMeButton extends StatefulWidget {
  final CourseVideo video;
  final VoidCallback? onNotificationToggled;

  const CompactNotifyMeButton({
    Key? key,
    required this.video,
    this.onNotificationToggled,
  }) : super(key: key);

  @override
  State<CompactNotifyMeButton> createState() => _CompactNotifyMeButtonState();
}

class _CompactNotifyMeButtonState extends State<CompactNotifyMeButton> {
  final WorkoutVideoNotificationService _notificationService = WorkoutVideoNotificationService();
  bool _isInNotifyList = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkNotifyStatus();
  }

  Future<void> _checkNotifyStatus() async {
    try {
      final isInList = await _notificationService.isVideoInNotifyMeList(widget.video.id);
      if (mounted) {
        setState(() {
          _isInNotifyList = isInList;
        });
      }
    } catch (e) {
      print('Error checking notify status: $e');
    }
  }

  Future<void> _toggleNotifyMe() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isInNotifyList) {
        await _notificationService.removeNotifyMeVideo(widget.video.id);
        setState(() {
          _isInNotifyList = false;
        });
      } else {
        await _notificationService.addNotifyMeVideo(widget.video.id, widget.video.title);
        setState(() {
          _isInNotifyList = true;
        });
      }

      widget.onNotificationToggled?.call();
    } catch (e) {
      print('Error toggling notify me: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: _isLoading ? null : _toggleNotifyMe,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _isInNotifyList 
              ? Colors.green.withOpacity(0.1)
              : theme.colorScheme.surface.withOpacity(0.8),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _isInNotifyList 
                ? Colors.green.withOpacity(0.3)
                : theme.colorScheme.outline.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: _isLoading
            ? SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _isInNotifyList ? Colors.green : theme.colorScheme.primary,
                  ),
                ),
              )
            : Icon(
                _isInNotifyList ? Icons.notifications_active : Icons.notifications_none,
                size: 16,
                color: _isInNotifyList ? Colors.green : theme.colorScheme.onSurface.withOpacity(0.7),
              ),
        ),
      ),
    );
  }
}
