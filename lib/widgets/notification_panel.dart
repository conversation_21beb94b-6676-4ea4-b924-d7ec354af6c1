import 'package:flutter/material.dart';
import 'dart:ui';
import '../design_system/kft_design_system.dart';
import '../services/awesome_notification_service.dart';

class NotificationPanel extends StatefulWidget {
  final VoidCallback? onClose;

  const NotificationPanel({
    super.key,
    this.onClose,
  });

  @override
  State<NotificationPanel> createState() => _NotificationPanelState();
}

class _NotificationPanelState extends State<NotificationPanel>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  final EnhancedNotificationService _notificationService = EnhancedNotificationService();
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();

    // Load real notifications asynchronously
    _loadNotifications();

    // Single optimized animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 250), // Reduced from 400ms
      vsync: this,
    );

    // Optimized slide animation with efficient curve
    _slideAnimation = Tween<double>(
      begin: 1.0, // Start from right instead of complex calculation
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutQuart, // More efficient than elasticOut
    ));

    // Optimized fade animation
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut, // Simpler curve for better performance
    ));

    // Optimized scale animation
    _scaleAnimation = Tween<double>(
      begin: 0.95, // Less dramatic scale change
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutQuart,
    ));

    // Start animation immediately
    _animationController.forward();
  }

  Future<void> _loadNotifications() async {
    try {
      await _notificationService.initialize();
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    } catch (e) {
      print('❌ Error loading notifications: $e');
      if (mounted) {
        setState(() {
          _isInitialized = true; // Still mark as initialized to show UI
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _closePanel() async {
    await _animationController.reverse();
    widget.onClose?.call();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final screenSize = MediaQuery.of(context).size;

    // Pre-calculate values for better performance
    final panelWidth = screenSize.width * 0.9;
    final panelHeight = screenSize.height * 0.7;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Stack(
          children: [
            // Optimized backdrop without blur for better performance
            GestureDetector(
              onTap: _closePanel,
              child: Container(
                color: Colors.black.withOpacity(0.4 * _fadeAnimation.value),
              ),
            ),

            // High-performance notification panel
            Positioned(
              top: 100,
              right: 16 + (_slideAnimation.value * panelWidth * 0.1),
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Opacity(
                  opacity: _fadeAnimation.value,
                  child: Container(
                    width: panelWidth,
                    height: panelHeight,
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? KFTDesignSystem.darkSurfaceColor
                          : Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.15),
                          blurRadius: 20, // Reduced from 30
                          offset: const Offset(0, 8), // Reduced from 10
                          spreadRadius: 1, // Reduced from 2
                        ),
                      ],
                    ),
                    child: child,
                  ),
                ),
              ),
            ),
          ],
        );
      },
      // Pre-built child to avoid rebuilding during animation
      child: Column(
        children: [
          // Optimized header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: theme.dividerColor.withOpacity(0.1),
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.notifications,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Notifications',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: _closePanel,
                  icon: Icon(
                    Icons.close,
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                  style: IconButton.styleFrom(
                    backgroundColor: theme.colorScheme.surface,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Optimized content area
          Expanded(
            child: _isInitialized
                ? Center(
                    child: Text(
                      'Notification history is not available. Please check your system notifications.',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  )
                : const Center(
                    child: CircularProgressIndicator(),
                  ),
          ),

          // Optimized footer
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: theme.dividerColor.withOpacity(0.1),
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () {
                      _closePanel();
                      Navigator.pushNamed(context, '/notification-settings');
                    },
                    child: Text(
                      'Settings',
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: theme.colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No notifications',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You\'re all caught up!',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.4),
            ),
          ),
        ],
      ),
    );
  }
}


