import 'package:flutter/material.dart';
import '../services/awesome_notification_service.dart';
import '../services/workout_video_notification_service.dart';

class SmartWorkoutPrompt {
  static Future<void> showPrompt(BuildContext context) async {
    final notificationService = EnhancedNotificationService();
    final videoNotificationService = WorkoutVideoNotificationService();

    // Check if smart workout setup is already completed
    if (await videoNotificationService.isSmartWorkoutSetupCompleted()) {
      return; // Don't show prompt again
    }

    // Get current time for smart suggestion
    final currentTime = TimeOfDay.now();

    // Show the prompt dialog
    if (context.mounted) {
      final result = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (context) => _SmartWorkoutPromptDialog(suggestedTime: currentTime),
      );

      if (result == true) {
        // User accepted - set up smart workout time
        final timeString = '${currentTime.hour.toString().padLeft(2, '0')}:${currentTime.minute.toString().padLeft(2, '0')}';

        try {
          // Set the smart workout time
          await videoNotificationService.setSmartWorkoutTime(timeString);

          // Enable workout notifications with the smart time
          await notificationService.setWorkoutReminderTime(timeString);
          await notificationService.setWorkoutNotificationEnabled(true);

          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.psychology, color: Colors.white, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text('Smart workout reminder set for ${_formatTime(currentTime)}! 🧠'),
                    ),
                  ],
                ),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                action: SnackBarAction(
                  label: 'Settings',
                  textColor: Colors.white,
                  onPressed: () {
                    Navigator.of(context).pushNamed('/notification-settings');
                  },
                ),
              ),
            );
          }
        } catch (e) {
          print('Error setting up smart workout time: $e');
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error setting up smart reminder: $e'),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        }
      } else {
        // User declined - mark as completed so we don't ask again
        await videoNotificationService.markSmartWorkoutSetupCompleted();

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('No problem! You can set a custom workout reminder time in Settings.'),
              behavior: SnackBarBehavior.floating,
              action: SnackBarAction(
                label: 'Settings',
                onPressed: () {
                  Navigator.of(context).pushNamed('/notification-settings');
                },
              ),
            ),
          );
        }
      }
    }
  }

  static String _formatTime(TimeOfDay time) {
    final hour = time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.period == DayPeriod.am ? 'AM' : 'PM';
    return '${hour == 0 ? 12 : hour}:$minute $period';
  }
}

class _SmartWorkoutPromptDialog extends StatelessWidget {
  final TimeOfDay suggestedTime;

  const _SmartWorkoutPromptDialog({required this.suggestedTime});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.fitness_center,
              color: theme.colorScheme.primary,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Great workout!',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'I noticed you just completed a workout. Would you like me to remind you to work out at this time (${suggestedTime.format(context)}) every day?',
            style: theme.textTheme.bodyLarge,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: theme.colorScheme.outline.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'This will help you build a consistent workout routine!',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: Text(
            'No, thanks',
            style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
          ),
        ),
        ElevatedButton.icon(
          onPressed: () => Navigator.of(context).pop(true),
          icon: const Icon(Icons.notifications_active, size: 18),
          label: const Text('Yes, remind me!'),
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: theme.colorScheme.onPrimary,
          ),
        ),
      ],
    );
  }
}
