import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/water_reminder_provider.dart';
import '../models/water_reminder.dart';
import '../services/water_goal_service.dart';
import 'water_goal_quick_edit_widget.dart';
import 'water_goal_settings_widget.dart';
import '../design_system/kft_design_system.dart';
import '../pages/water_reminder_page.dart';
import 'package:intl/intl.dart';
import '../theme/app_theme.dart';
import '../services/awesome_notification_service.dart';
import 'dart:io';

/// Minimalistic hydration reminder widget for the home screen
/// Features: Progress tracking, quick add buttons, interval settings, and history
class HydrationHomeWidget extends StatefulWidget {
  const HydrationHomeWidget({super.key});

  @override
  State<HydrationHomeWidget> createState() => _HydrationHomeWidgetState();
}

class _HydrationHomeWidgetState extends State<HydrationHomeWidget>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;
  bool _isProcessing = false;
  bool isLoading = false;
  late WaterReminderProvider provider;

  @override
  void initState() {
    super.initState();
    provider = Provider.of<WaterReminderProvider>(context, listen: false);
    WidgetsBinding.instance.addObserver(this);

    // Initialize only essential animation controller
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Create smooth progress animation
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeOutCubic,
    ));

    // --- AUTO-ENABLE HYDRATION REMINDER IF NOT SET ---
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // Only run if no reminder exists
      if (provider.waterReminder == null) {
        // Get the default goal from the provider's public method
        final defaultGoal = await provider.getDefaultGoalInMl();
        await provider.setupWaterReminder(defaultGoal, 2); // 2 hours, notifications enabled by default
      }
    });
    // --- END AUTO-ENABLE ---
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _progressController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Always refresh water logs when dependencies change (e.g., widget is shown)
    provider.refreshWaterReminder();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Refresh water logs when app is resumed
      provider.refreshWaterReminder();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Consumer<WaterReminderProvider>(
      builder: (context, provider, child) {
        final reminder = provider.waterReminder;
        final isLoading = provider.isLoading;

        // Animate progress when data changes
        if (reminder != null) {
          _progressController.animateTo(reminder.progressPercentage);
        }

        return Stack(
          children: [
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.07),
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: theme.colorScheme.primary.withOpacity(0.15),
                  width: 1,
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () => _navigateToWaterReminderPage(context),
                  borderRadius: BorderRadius.circular(10),
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        isLoading && reminder == null
                            ? _buildLoadingState(theme)
                            : _buildContent(context, theme, reminder, provider),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildLoadingState(ThemeData theme) {
    return _ShimmerLoading(
      isLoading: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header skeleton
          Row(
      children: [
        Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurface.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
          ),
        ),
        const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 120,
                      height: 16,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.onSurface.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      width: 160,
                      height: 12,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.onSurface.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
          ),
        ),
      ],
                ),
              ),
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurface.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Progress bar skeleton
          Container(
            height: 8,
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurface.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 12),
          // Stats skeleton
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: 80,
                height: 16,
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurface.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              Container(
                width: 60,
                height: 16,
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurface.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              Container(
                width: 80,
                height: 16,
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurface.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Quick actions skeleton
          Row(
            children: [
              ...List.generate(3, (index) => Padding(
                padding: EdgeInsets.only(right: 8),
                child: Container(
                  width: 60,
                  height: 32,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.onSurface.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
              )),
              const Spacer(),
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurface.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    ThemeData theme,
    WaterReminder? reminder,
    WaterReminderProvider provider,
  ) {
    if (reminder == null) {
      return _buildEmptyState(theme);
    }

    final isDark = theme.brightness == Brightness.dark;
    final currentIntake = reminder.currentIntake;

    // Use goal from goal service if available, otherwise fall back to reminder target
    final goalSettings = provider.goalSettings;
    final targetIntake = goalSettings?.goalInMl ?? reminder.targetIntake;

    // Recalculate progress with current goal
    final progress = targetIntake > 0 ? (currentIntake / targetIntake).clamp(0.0, 1.0) : 0.0;
    final remainingIntake = (targetIntake - currentIntake).clamp(0, targetIntake);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Water-inspired header
        Row(
          children: [
            // Animated water drop icon with gradient
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(
                Icons.water_drop,
                color: Colors.white,
                size: 16,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Stay Hydrated',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  Text(
                    'Keep your body refreshed',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
            ),
            // Water wave settings button
            GestureDetector(
              onTap: () => _showIntervalSettings(context),
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.tune,
                  color: theme.colorScheme.primary,
                  size: 12,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Progress section
        _buildProgressSection(theme, progress, currentIntake, targetIntake, provider),

        const SizedBox(height: 12),

        // Goal quick edit
        _buildGoalQuickEdit(context, theme, provider, reminder),

        const SizedBox(height: 16),

        // Quick actions
        _buildQuickActionsSection(context, theme, provider, remainingIntake, reminder),
      ],
    );
  }

  Widget _buildGoalQuickEdit(
    BuildContext context,
    ThemeData theme,
    WaterReminderProvider provider,
    WaterReminder? reminder,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: WaterGoalQuickEditWidget(
        padding: EdgeInsets.zero,
        onGoalUpdated: () {
          // Refresh the water reminder data when goal is updated
          provider.refreshWaterReminder();
        },
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    final isDark = theme.brightness == Brightness.dark;
    final TextEditingController _goalController = TextEditingController();
    int _selectedInterval = 2;

    return StatefulBuilder(
      builder: (context, setState) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    Icons.water_drop,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Set up your daily water reminder',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Enhanced goal input with unit selection
            _buildGoalInputSection(_goalController),
            const SizedBox(height: 12),
            DropdownButtonFormField<int>(
              value: _selectedInterval,
              decoration: InputDecoration(
                labelText: 'Reminder Interval (hours)',
                border: OutlineInputBorder(),
              ),
              items: [1, 2, 3, 4, 6, 8]
                  .map((v) => DropdownMenuItem(
                        value: v,
                        child: Text('Every $v hour${v > 1 ? 's' : ''}'),
                      ))
                  .toList(),
              onChanged: (v) => setState(() => _selectedInterval = v ?? 2),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  final goalText = _goalController.text.trim();
                  if (goalText.isEmpty) return;

                  final goalValue = double.tryParse(goalText);
                  if (goalValue == null) return;

                  // Convert to ml if needed (assuming liters by default for simplicity)
                  final goalInMl = goalValue > 100 ? goalValue.round() : WaterGoalService.litersToMl(goalValue);

                  final provider = Provider.of<WaterReminderProvider>(context, listen: false);
                  await provider.setupWaterReminder(goalInMl, _selectedInterval);
                  FocusScope.of(context).unfocus();
                },
                child: const Text('Save & Start'),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildGoalInputSection(TextEditingController goalController) {
    return StatefulBuilder(
      builder: (context, setState) {
        WaterUnit selectedUnit = WaterUnit.liters;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Unit selector
            Row(
              children: [
                Text(
                  'Goal Unit:',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 12),
                ...WaterUnit.values.map((unit) {
                  final isSelected = unit == selectedUnit;
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          selectedUnit = unit;
                          // Update controller with converted value
                          final currentValue = double.tryParse(goalController.text) ?? 2.0;
                          if (unit == WaterUnit.milliliters) {
                            goalController.text = WaterGoalService.litersToMl(currentValue).toString();
                          } else {
                            goalController.text = WaterGoalService.mlToLiters(currentValue.round()).toStringAsFixed(1);
                          }
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: isSelected
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(context).colorScheme.outline,
                          ),
                        ),
                        child: Text(
                          unit.fullName,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: isSelected
                                ? Colors.white
                                : Theme.of(context).colorScheme.onSurface,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ],
            ),
            const SizedBox(height: 12),

            // Goal input field
            TextField(
              controller: goalController,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              decoration: InputDecoration(
                labelText: 'Daily Water Goal',
                hintText: selectedUnit == WaterUnit.liters ? '2.0' : '2000',
                suffixText: selectedUnit.displayName,
                border: const OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),

            // Quick preset buttons
            Wrap(
              spacing: 8,
              children: WaterGoalService.getGoalPresets().map((preset) {
                final displayText = WaterGoalService.formatGoalDisplay(preset, selectedUnit);
                return GestureDetector(
                  onTap: () {
                    final goalValue = selectedUnit == WaterUnit.liters
                        ? WaterGoalService.mlToLiters(preset)
                        : preset.toDouble();
                    goalController.text = selectedUnit == WaterUnit.liters
                        ? goalValue.toStringAsFixed(1)
                        : goalValue.toInt().toString();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                      ),
                    ),
                    child: Text(
                      displayText,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        );
      },
    );
  }

  Widget _buildProgressSection(
    ThemeData theme,
    double progress,
    int currentIntake,
    int targetIntake,
    WaterReminderProvider provider,
  ) {
    final isDark = theme.brightness == Brightness.dark;
    final progressColor = progress >= 1.0
        ? theme.colorScheme.primary
        : theme.colorScheme.primary;

    return Column(
      children: [
        // Water-inspired progress bar with wave effect
        Container(
          height: 8,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withOpacity(0.07),
            borderRadius: BorderRadius.circular(4),
          ),
          child: AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return Stack(
                children: [
                  // Background wave pattern
                  Container(
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.07),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  // Progress fill with gradient
                  FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: _progressAnimation.value * progress,
                    child: Container(
                      decoration: BoxDecoration(
                        color: progressColor,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ),

        const SizedBox(height: 12),

        // Enhanced stats with water-inspired styling
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Current intake with water drop
            Row(
              children: [
                Icon(
                  Icons.water_drop,
                  size: 12,
                  color: progressColor,
                ),
                const SizedBox(width: 4),
                Text(
                  '${currentIntake}ml',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: progressColor,
                  ),
                ),
              ],
            ),
            // Progress percentage
            if (progress > 0) ...[
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.07),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${(progress * 100).round()}%',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: progressColor,
                    fontWeight: FontWeight.w500,
                    fontSize: 11,
                  ),
                ),
              ),
            ],
            // Target with goal icon
            Row(
              children: [
                Icon(
                  Icons.flag_outlined,
                  size: 12,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 4),
                Text(
                  provider.getFormattedGoal(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionsSection(
    BuildContext context,
    ThemeData theme,
    WaterReminderProvider provider,
    int remainingIntake,
    WaterReminder reminder,
  ) {
    return Row(
      children: [
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildMinimalAddButton(context, theme, '+50', 50, provider),
                const SizedBox(width: 8),
                _buildMinimalAddButton(context, theme, '+100', 100, provider),
                const SizedBox(width: 8),
                _buildMinimalAddButton(context, theme, '+150', 150, provider),
              ],
            ),
          ),
        ),
        // Minimal history button
        _buildMinimalHistoryButton(context, theme, reminder),
      ],
    );
  }

  Widget _buildMinimalAddButton(
    BuildContext context,
    ThemeData theme,
    String label,
    int amount,
    WaterReminderProvider provider,
  ) {
    final isDark = theme.brightness == Brightness.dark;

    return GestureDetector(
      onTap: () => _addWater(context, provider, amount),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
        decoration: BoxDecoration(
          color: theme.colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: theme.colorScheme.primary.withOpacity(0.15),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.add,
              size: 12,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 4),
            Text(
              label.substring(1), // Remove the '+' since we have an icon
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMinimalHistoryButton(
    BuildContext context,
    ThemeData theme,
    WaterReminder reminder,
  ) {
    final isDark = theme.brightness == Brightness.dark;
    final todayLogs = reminder.logs.where((log) {
      final logDate = DateTime(log.timestamp.year, log.timestamp.month, log.timestamp.day);
      final today = DateTime.now();
      return logDate.year == today.year &&
          logDate.month == today.month &&
          logDate.day == today.day;
    }).toList();

    return GestureDetector(
      onTap: () => _showTodayHistory(context, reminder),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: theme.colorScheme.outline.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Icon(
          Icons.history,
          size: 16,
          color: theme.colorScheme.onSurface,
        ),
      ),
    );
  }

  Widget _buildDragHandle() {
    return Container(
      width: 40,
      height: 4,
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  void _addWater(BuildContext context, WaterReminderProvider provider, int amount) {
    final theme = Theme.of(context);
    // Add haptic feedback
    HapticFeedback.lightImpact();

    // Add water intake
    provider.addWaterIntake(amount);

    // Show water-inspired success feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.water_drop,
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              'Added ${amount}ml 💧',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        backgroundColor: theme.colorScheme.primary,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _navigateToWaterReminderPage(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const WaterReminderPage()),
    );
  }

  /// Shows a modal bottom sheet for interval settings.
  void _showIntervalSettings(BuildContext context) {
    final provider = Provider.of<WaterReminderProvider>(context, listen: false);
    int currentIntervalInHours = (provider.waterReminder?.reminderInterval ?? 2);
    int currentDailyTarget = (provider.waterReminder?.targetIntake ?? 2000);
    bool isNotificationsEnabled = provider.waterReminder?.isNotificationsEnabled ?? true;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            final theme = Theme.of(context);
            return DraggableScrollableSheet(
              initialChildSize: 0.4,
              minChildSize: 0.3,
              maxChildSize: 0.5,
              expand: false,
              builder: (_, controller) {
                return Container(
                  decoration: BoxDecoration(
                    color: theme.cardColor,
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                  ),
                  child: Column(
                    children: [
                      _buildDragHandle(),
                      Expanded(
                        child: SingleChildScrollView(
                          controller: controller,
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Water Reminder Settings',
                                style: theme.textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 20),
                              // Interval settings
                              Text(
                                'Reminder Interval: ${currentIntervalInHours} hour${currentIntervalInHours > 1 ? 's' : ''}',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Slider(
                                value: currentIntervalInHours.toDouble(),
                                min: 0,
                                max: 24,
                                divisions: 24,
                                onChanged: (value) {
                                  setModalState(() {
                                    currentIntervalInHours = value.round();
                                  });
                                },
                              ),
                              const SizedBox(height: 20),
                              // Toggle for notifications
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Enable Notifications',
                                    style: theme.textTheme.titleMedium,
                                  ),
                                  Switch(
                                    value: isNotificationsEnabled,
                                    onChanged: (bool value) {
                                      setModalState(() {
                                        isNotificationsEnabled = value;
                                      });
                                    },
                                  ),
                                ],
                              ),
                              const SizedBox(height: 20),
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton(
                                  onPressed: () async {
                                    if (context.mounted) {
                                      // Check if enabling notifications for the first time or changing liters/hours with notifications enabled
                                      final notificationService = EnhancedNotificationService();
                                      final hadPermission = await notificationService.hasPermissions();
                                      bool permissionGranted = true;
                                      final prevReminder = provider.waterReminder;
                                      final prevTarget = prevReminder?.targetIntake ?? 2000;
                                      final prevInterval = prevReminder?.reminderInterval ?? 2;
                                      final targetChanged = currentDailyTarget != prevTarget;
                                      final intervalChanged = currentIntervalInHours != prevInterval;
                                      if (isNotificationsEnabled && (!hadPermission || targetChanged || intervalChanged)) {
                                        permissionGranted = await notificationService.requestPermissionsWithExplanation(context);
                                      }
                                      if (!permissionGranted) {
                                        if (context.mounted) {
                                          ScaffoldMessenger.of(context).showSnackBar(
                                            KFTDesignSystem.errorSnackBar('Notification permission denied. Please enable notifications in settings.'),
                                          );
                                        }
                                        return;
                                      }
                                      await _updateNotificationSettings(
                                        context,
                                        provider,
                                        currentIntervalInHours,
                                        currentDailyTarget,
                                        isNotificationsEnabled,
                                        0,
                                      );
                                      Navigator.pop(context);
                                    }
                                  },
                                  child: const Text('Save Settings'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: theme.colorScheme.primary,
                                    foregroundColor: theme.colorScheme.onPrimary,
                                    padding: const EdgeInsets.symmetric(vertical: 15),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  Future<void> _updateNotificationSettings(
    BuildContext context,
    WaterReminderProvider provider,
    int interval,
    int dailyTarget,
    bool enableNotifications,
    int testIntervalSeconds,
  ) async {
    try {
      if (!context.mounted) return;

      // Show processing indicator
      ScaffoldMessenger.of(context).showSnackBar(
        KFTDesignSystem.infoSnackBar('Updating settings...'),
      );

      // First, save the settings
      await provider.saveWaterReminderSettings(
        interval: interval,
        targetIntake: dailyTarget,
        isNotificationsEnabled: enableNotifications,
      );

      // Then handle notifications
      final notificationService = EnhancedNotificationService();
      
      if (enableNotifications) {
        // Request permissions first
        final hasPermission = await notificationService.requestPermissions();
        if (!hasPermission) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              KFTDesignSystem.errorSnackBar('Notification permission denied. Please enable notifications in settings.'),
            );
          }
          return;
        }

        if (testIntervalSeconds > 0) {
          // Schedule test notification
          await notificationService.showWaterReminderNotification(
            intervalInSeconds: testIntervalSeconds,
          );
        }

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            KFTDesignSystem.successSnackBar('Water reminders updated successfully!'),
          );
        }
      } else {
        // Cancel all water reminders if notifications are disabled
        await notificationService.cancelWaterReminders();
        await notificationService.setWaterNotificationEnabled(false);
        
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            KFTDesignSystem.infoSnackBar('Water reminders disabled'),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          KFTDesignSystem.errorSnackBar('Failed to update settings: $e'),
        );
      }
    }
  }

  void _showTodayHistory(BuildContext context, WaterReminder reminder) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final today = DateTime.now();
    final todayLogs = reminder.logs.where((log) =>
      log.timestamp.year == today.year &&
      log.timestamp.month == today.month &&
      log.timestamp.day == today.day
    ).toList();

    showModalBottomSheet(
      context: context,
      backgroundColor: isDark ? Colors.grey[900] : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Today's Water Intake",
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 16),
                if (todayLogs.isEmpty)
                  Text(
                    'No water intake logged yet today.',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  )
                else
                  ...todayLogs.map((log) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 6),
                    child: Row(
                      children: [
                        Icon(Icons.water_drop, color: theme.colorScheme.primary, size: 18),
                        const SizedBox(width: 10),
                        Text(
                          '+${log.amount}ml',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          log.formattedTime,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  )),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.blue,
      ),
    );
  }

  Future<void> _saveSettings(WaterReminder reminder) async {
    try {
      await provider.saveWaterReminderSettings(
        interval: reminder.reminderInterval,
        targetIntake: reminder.targetIntake,
        isNotificationsEnabled: reminder.isNotificationsEnabled,
      );
      _showSnackBar('Settings saved successfully!');
    } catch (e) {
      _showSnackBar('Error saving settings: $e', isError: true);
    }
  }

  Future<void> _scheduleReminders(WaterReminder reminder) async {
    try {
      final notificationService = EnhancedNotificationService();
      await notificationService.scheduleWaterReminders(
        dailyTarget: reminder.targetIntake,
        intervalHours: reminder.reminderInterval,
      );
      _showSnackBar('Reminders scheduled successfully!');
    } catch (e) {
      _showSnackBar('Error scheduling reminders: $e', isError: true);
    }
  }
}

class _ShimmerLoading extends StatefulWidget {
  final Widget child;
  final bool isLoading;

  const _ShimmerLoading({
    required this.child,
    required this.isLoading,
  });

  @override
  State<_ShimmerLoading> createState() => _ShimmerLoadingState();
}

class _ShimmerLoadingState extends State<_ShimmerLoading> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();
    _animation = Tween<double>(begin: -2, end: 2).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOutSine),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isLoading) return widget.child;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          blendMode: BlendMode.srcATop,
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment(_animation.value - 1, 0),
              end: Alignment(_animation.value + 1, 0),
              colors: [
                Colors.transparent,
                Colors.white.withOpacity(0.5),
                Colors.transparent,
              ],
              stops: const [0.0, 0.5, 1.0],
            ).createShader(bounds);
          },
          child: child,
        );
      },
      child: widget.child,
    );
  }
}
