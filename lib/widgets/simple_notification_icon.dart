import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// High-performance, minimalistic notification icon for the top navigation bar
class SimpleNotificationIcon extends StatefulWidget {
  final Color? iconColor;
  final double iconSize;
  final VoidCallback? onPressed;
  final bool showBadge;
  final int badgeCount;

  const SimpleNotificationIcon({
    super.key,
    this.iconColor,
    this.iconSize = 24,
    this.onPressed,
    this.showBadge = false,
    this.badgeCount = 0,
  });

  @override
  State<SimpleNotificationIcon> createState() => _SimpleNotificationIconState();
}

class _SimpleNotificationIconState extends State<SimpleNotificationIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    // Optimized animation with shorter duration and efficient curve
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100), // Reduced from 150ms
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.96, // Less dramatic scale for better performance
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut, // More efficient curve than easeInOut
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (!_isPressed) {
      _isPressed = true;
      // Optimized haptic feedback - only on first press
      HapticFeedback.selectionClick(); // Lighter than lightImpact
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    _isPressed = false;
    _animationController.reverse();
  }

  void _onTapCancel() {
    _isPressed = false;
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveIconColor = widget.iconColor ??
        (theme.brightness == Brightness.dark ? Colors.white : Colors.white);

    // Pre-build badge widget to avoid rebuilding in AnimatedBuilder
    Widget? badgeWidget;
    if (widget.showBadge) {
      if (widget.badgeCount > 0) {
        badgeWidget = Positioned(
          top: -2,
          right: -2,
          child: Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.red,
              shape: BoxShape.circle,
              border: Border.all(
                color: theme.scaffoldBackgroundColor,
                width: 1,
              ),
            ),
            constraints: const BoxConstraints(
              minWidth: 16,
              minHeight: 16,
            ),
            child: widget.badgeCount > 99
                ? const Text(
                    '99+',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 8,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  )
                : Text(
                    '${widget.badgeCount}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
          ),
        );
      } else {
        badgeWidget = Positioned(
          top: 2,
          right: 2,
          child: Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: Colors.red,
              shape: BoxShape.circle,
              border: Border.all(
                color: theme.scaffoldBackgroundColor,
                width: 1,
              ),
            ),
          ),
        );
      }
    }

    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      onTap: widget.onPressed,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: child,
          );
        },
        // Pre-built child to avoid rebuilding
        child: Container(
          padding: const EdgeInsets.all(8),
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              // High-performance notification icon
              Icon(
                Icons.notifications_none_outlined,
                color: effectiveIconColor,
                size: widget.iconSize,
              ),

              // Pre-built badge widget
              if (badgeWidget != null) badgeWidget,
            ],
          ),
        ),
      ),
    );
  }
}

/// Enhanced notification icon wrapper for app bars
class EnhancedNotificationIcon extends StatelessWidget {
  final Color? iconColor;
  final double iconSize;
  final VoidCallback? onPressed;

  const EnhancedNotificationIcon({
    super.key,
    this.iconColor,
    this.iconSize = 24,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return SimpleNotificationIcon(
      iconColor: iconColor,
      iconSize: iconSize,
      onPressed: onPressed,
      showBadge: false, // Can be enhanced later with actual notification count
      badgeCount: 0,
    );
  }
}
