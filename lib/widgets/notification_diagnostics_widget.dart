import 'package:flutter/material.dart';
import '../services/awesome_notification_service.dart';

class NotificationDiagnosticsWidget extends StatefulWidget {
  const NotificationDiagnosticsWidget({Key? key}) : super(key: key);

  @override
  State<NotificationDiagnosticsWidget> createState() => _NotificationDiagnosticsWidgetState();
}

class _NotificationDiagnosticsWidgetState extends State<NotificationDiagnosticsWidget> {
  String _diagnostics = '';
  bool _loading = false;

  Future<void> _runDiagnostics() async {
    setState(() {
      _loading = false;
      _diagnostics = 'Diagnostics are not available in this build.';
    });
  }

  Future<void> _showImmediateNotification() async {
    final notificationService = EnhancedNotificationService();
    await notificationService.sendTestNotification(
      title: 'Immediate Test',
      body: 'This is an immediate notification - should appear in 3 seconds!',
      delaySeconds: 3,
    );
    await _runDiagnostics();
  }

  Future<void> _schedule10sNotification() async {
    final notificationService = EnhancedNotificationService();
    await notificationService.sendTestNotification(
      title: 'Test 10s Notification',
      body: 'This notification was scheduled for 10 seconds!',
      delaySeconds: 10,
    );
    await _runDiagnostics();
  }

  Future<void> _checkAndroidPermissions() async {
    final notificationService = EnhancedNotificationService();
    await notificationService.checkAndroidSchedulingPermissions();
    await _runDiagnostics();
  }

  Future<void> _sendBasicTest() async {
    final notificationService = EnhancedNotificationService();
    await notificationService.sendTestNotification(
      title: 'Basic Test',
      body: 'This is a basic test notification!',
      delaySeconds: 5,
    );
    await _runDiagnostics();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Notification Diagnostics')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ElevatedButton(
              onPressed: _loading ? null : _runDiagnostics,
              child: const Text('Run Diagnostics'),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: _loading ? null : _showImmediateNotification,
              child: const Text('Show Immediate Notification'),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: _loading ? null : _sendBasicTest,
              child: const Text('Send Basic Test'),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: _loading ? null : _schedule10sNotification,
              child: const Text('Schedule Notification (10s)'),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: _loading ? null : _checkAndroidPermissions,
              child: const Text('Test Android Scheduling (2s)'),
            ),
            const SizedBox(height: 24),
            Expanded(
              child: SingleChildScrollView(
                child: Text(_diagnostics, style: const TextStyle(fontFamily: 'monospace')),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 