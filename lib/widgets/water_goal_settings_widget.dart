import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/water_reminder_provider.dart';
import '../services/water_goal_service.dart';
import '../services/awesome_notification_service.dart';
import '../design_system/kft_design_system.dart';

/// Comprehensive water goal settings widget
/// Provides goal editing, unit switching, presets, and quick adjustments
class WaterGoalSettingsWidget extends StatefulWidget {
  final bool showAsBottomSheet;
  final VoidCallback? onGoalUpdated;
  
  const WaterGoalSettingsWidget({
    Key? key,
    this.showAsBottomSheet = true,
    this.onGoalUpdated,
  }) : super(key: key);

  @override
  State<WaterGoalSettingsWidget> createState() => _WaterGoalSettingsWidgetState();
}

class _WaterGoalSettingsWidgetState extends State<WaterGoalSettingsWidget> {
  late TextEditingController _goalController;
  late WaterUnit _selectedUnit;
  late int _currentGoalInMl;
  bool _isLoading = false;
  String? _errorMessage;
  final _notificationService = EnhancedNotificationService();
  int _selectedInterval = 2;
  bool _isNotificationsEnabled = true;

  @override
  void initState() {
    super.initState();
    _goalController = TextEditingController();
    _initializeSettings();
    _loadCurrentSettings();
  }

  void _initializeSettings() {
    final provider = Provider.of<WaterReminderProvider>(context, listen: false);
    final settings = provider.goalSettings ?? WaterGoalSettings.defaultSettings();
    
    _selectedUnit = settings.preferredUnit;
    _currentGoalInMl = settings.goalInMl;
    _updateControllerText();
  }

  Future<void> _loadCurrentSettings() async {
    final provider = Provider.of<WaterReminderProvider>(context, listen: false);
    if (provider.waterReminder != null) {
      setState(() {
        _selectedInterval = provider.waterReminder!.reminderInterval;
        _isNotificationsEnabled = provider.waterReminder!.isNotificationsEnabled;
      });
    }
  }

  void _updateControllerText() {
    final goalInUnit = _selectedUnit == WaterUnit.liters 
        ? WaterGoalService.mlToLiters(_currentGoalInMl)
        : _currentGoalInMl.toDouble();
    
    _goalController.text = _selectedUnit == WaterUnit.liters 
        ? goalInUnit.toStringAsFixed(1)
        : goalInUnit.toInt().toString();
  }

  @override
  void dispose() {
    _goalController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.showAsBottomSheet) {
      return _buildBottomSheet(context);
    } else {
      return _buildInlineWidget(context);
    }
  }

  Widget _buildBottomSheet(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: 16,
        bottom: MediaQuery.of(context).viewInsets.bottom + 16,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(context),
          const SizedBox(height: 20),
          _buildContent(context),
        ],
      ),
    );
  }

  Widget _buildInlineWidget(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 16),
            _buildContent(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);
    return Row(
      children: [
        Icon(
          Icons.flag,
          color: theme.colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          'Daily Water Goal',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        if (widget.showAsBottomSheet)
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
          ),
      ],
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildCurrentGoalDisplay(context),
        const SizedBox(height: 16),
        _buildUnitSelector(context),
        const SizedBox(height: 16),
        _buildGoalInput(context),
        const SizedBox(height: 16),
        _buildPresetButtons(context),
        const SizedBox(height: 16),
        _buildQuickAdjustments(context),
        const SizedBox(height: 20),
        _buildActionButtons(context),
        if (_errorMessage != null) ...[
          const SizedBox(height: 12),
          _buildErrorMessage(context),
        ],
      ],
    );
  }

  Widget _buildCurrentGoalDisplay(BuildContext context) {
    final theme = Theme.of(context);
    final provider = Provider.of<WaterReminderProvider>(context);
    final currentGoal = provider.getFormattedGoal();
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.water_drop,
            color: theme.colorScheme.primary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            'Current Goal: ',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            currentGoal,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnitSelector(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Unit',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: WaterUnit.values.map((unit) {
            final isSelected = unit == _selectedUnit;
            return Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                  right: unit == WaterUnit.milliliters ? 8 : 0,
                ),
                child: GestureDetector(
                  onTap: () => _onUnitChanged(unit),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? theme.colorScheme.primary 
                          : theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected 
                            ? theme.colorScheme.primary 
                            : theme.colorScheme.outline,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        unit.fullName,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: isSelected 
                              ? Colors.white 
                              : theme.colorScheme.onSurface,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildGoalInput(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Goal Amount',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _goalController,
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
                decoration: InputDecoration(
                  hintText: _selectedUnit == WaterUnit.liters ? '2.0' : '2000',
                  suffixText: _selectedUnit.displayName,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 16,
                  ),
                ),
                onChanged: _onGoalInputChanged,
              ),
            ),
            const SizedBox(width: 12),
            _buildSlider(context),
          ],
        ),
      ],
    );
  }

  Widget _buildSlider(BuildContext context) {
    final theme = Theme.of(context);
    final minValue = _selectedUnit == WaterUnit.liters ? 0.5 : 500.0;
    final maxValue = _selectedUnit == WaterUnit.liters ? 5.0 : 5000.0;
    final currentValue = _selectedUnit == WaterUnit.liters
        ? WaterGoalService.mlToLiters(_currentGoalInMl)
        : _currentGoalInMl.toDouble();

    return Expanded(
      flex: 2,
      child: Column(
        children: [
          Text(
            'Adjust',
            style: theme.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          Slider(
            value: currentValue.clamp(minValue, maxValue),
            min: minValue,
            max: maxValue,
            divisions: _selectedUnit == WaterUnit.liters ? 18 : 18,
            onChanged: _onSliderChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildPresetButtons(BuildContext context) {
    final theme = Theme.of(context);
    final presets = WaterGoalService.getGoalPresets();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Presets',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: presets.map((preset) {
            final isSelected = preset == _currentGoalInMl;
            final displayText = WaterGoalService.formatGoalDisplay(preset, _selectedUnit);

            return GestureDetector(
              onTap: () => _onPresetSelected(preset),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected
                      ? theme.colorScheme.primary
                      : theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.outline,
                  ),
                ),
                child: Text(
                  displayText,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isSelected
                        ? Colors.white
                        : theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildQuickAdjustments(BuildContext context) {
    final theme = Theme.of(context);
    final adjustments = WaterGoalService.getQuickAdjustments();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Adjustments',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: adjustments.map((adjustment) {
                  return Column(
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildAdjustmentButton(
                            context,
                            Icons.remove,
                            () => _adjustGoal(-adjustment),
                          ),
                          const SizedBox(width: 8),
                          _buildAdjustmentButton(
                            context,
                            Icons.add,
                            () => _adjustGoal(adjustment),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${adjustment}ml',
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAdjustmentButton(
    BuildContext context,
    IconData icon,
    VoidCallback onPressed,
  ) {
    final theme = Theme.of(context);
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: theme.colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: theme.colorScheme.primary.withOpacity(0.3),
          ),
        ),
        child: Icon(
          icon,
          size: 16,
          color: theme.colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final theme = Theme.of(context);
    return Row(
      children: [
        if (widget.showAsBottomSheet)
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ),
        if (widget.showAsBottomSheet) const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveGoal,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save Goal'),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorMessage(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.error.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.error.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: theme.colorScheme.error,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _errorMessage!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Event handlers
  void _onUnitChanged(WaterUnit unit) {
    setState(() {
      _selectedUnit = unit;
      _updateControllerText();
    });
  }

  void _onGoalInputChanged(String value) {
    if (value.isEmpty) return;

    final parsedValue = double.tryParse(value);
    if (parsedValue == null) return;

    setState(() {
      _currentGoalInMl = _selectedUnit == WaterUnit.liters
          ? WaterGoalService.litersToMl(parsedValue)
          : parsedValue.round();
      _errorMessage = null;
    });
  }

  void _onSliderChanged(double value) {
    setState(() {
      _currentGoalInMl = _selectedUnit == WaterUnit.liters
          ? WaterGoalService.litersToMl(value)
          : value.round();
      _updateControllerText();
      _errorMessage = null;
    });
  }

  void _onPresetSelected(int presetInMl) {
    setState(() {
      _currentGoalInMl = presetInMl;
      _updateControllerText();
      _errorMessage = null;
    });

    // Add haptic feedback
    HapticFeedback.lightImpact();
  }

  void _adjustGoal(int adjustmentInMl) {
    setState(() {
      _currentGoalInMl = (_currentGoalInMl + adjustmentInMl).clamp(500, 5000);
      _updateControllerText();
      _errorMessage = null;
    });

    // Add haptic feedback
    HapticFeedback.lightImpact();
  }

  Future<void> _saveGoal() async {
    if (!WaterGoalService.isValidGoal(_currentGoalInMl)) {
      setState(() {
        _errorMessage = 'Goal must be between 500ml and 5000ml';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    final provider = Provider.of<WaterReminderProvider>(context, listen: false);
    final hasPermission = await _notificationService.hasPermissions();
    if (!hasPermission) {
      final granted = await _notificationService.requestPermissionsWithExplanation(context);
      if (!granted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Notification permission is required for water reminders.';
        });
        return;
      }
    }

    try {
      // Update preferred unit first
      await provider.updatePreferredUnit(_selectedUnit);
      // Update daily goal
      await provider.updateDailyGoal(_currentGoalInMl);
      // Add haptic feedback
      HapticFeedback.mediumImpact();
      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text('Goal updated to ${WaterGoalService.formatGoalDisplay(_currentGoalInMl, _selectedUnit)}'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
      widget.onGoalUpdated?.call();
      if (widget.showAsBottomSheet && mounted) {
        Navigator.of(context).pop();
      }
      if (mounted) {
        try {
          final provider = Provider.of<WaterReminderProvider>(context, listen: false);
          provider.refreshWaterReminder();
        } catch (e) {
          debugPrint('Could not refresh water reminder: $e');
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to save goal: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _handleNotificationPermission(BuildContext context, WaterReminderProvider provider) async {
    setState(() => _isLoading = true);

    try {
      final hasPermission = await _notificationService.requestPermissionsWithExplanation(context);
      
      if (hasPermission) {
        // Update reminder settings with notifications enabled
        if (provider.waterReminder != null) {
          await provider.saveWaterReminderSettings(
            interval: _selectedInterval,
            targetIntake: provider.waterReminder!.targetIntake,
            isNotificationsEnabled: true,
          );
          setState(() => _isNotificationsEnabled = true);
        }
      } else {
        // Show message if permission denied
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Notifications are required for water reminders. Please enable them in settings.'),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  // Static method to show as bottom sheet
  static Future<void> showAsBottomSheet(BuildContext context) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const WaterGoalSettingsWidget(showAsBottomSheet: true),
    );
  }
}
