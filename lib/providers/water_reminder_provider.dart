import 'dart:async';
import 'package:flutter/material.dart';
import '../models/water_reminder.dart';
import '../services/water_reminder_service.dart';
import '../services/awesome_notification_service.dart';
import '../services/water_goal_service.dart';

class WaterReminderProvider extends ChangeNotifier {
  final WaterReminderService _service = WaterReminderService();
  final WaterGoalService _goalService = WaterGoalService();

  WaterReminder? _waterReminder;
  WaterGoalSettings? _goalSettings;
  bool _isLoading = false;
  String? _error;
  Timer? _refreshTimer;

  // Getters
  WaterReminder? get waterReminder => _waterReminder;
  WaterGoalSettings? get goalSettings => _goalSettings;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Constructor
  WaterReminderProvider() {
    // Load water reminder data on initialization
    loadWaterReminder();
    loadGoalSettings();

    // Set up periodic refresh (every 5 minutes)
    _refreshTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      refreshWaterReminder();
    });

    // Initialize periodic checks
    initializePeriodicChecks();
  }

  // Load goal settings
  Future<void> loadGoalSettings() async {
    try {
      _goalSettings = await _goalService.getGoalSettings();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading goal settings: $e');
    }
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  // Load water reminder data
  Future<void> loadWaterReminder() async {
    if (_isLoading) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final reminder = await _service.getWaterReminder();
      _waterReminder = reminder;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to load water reminder data: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Refresh water reminder data from backend
  Future<void> refreshWaterReminder() async {
    try {
      // Force refresh from backend to get latest data
      final reminder = await _service.forceRefreshWaterData();

      // Handle nullable reminder (admin may not have configured it)
      if (reminder == null) {
        // Check if user has their own settings
        final hasUserSettings = await _service.hasUserSettings();
        if (hasUserSettings) {
          final userReminder = await _service.getUserWaterReminder();
          _waterReminder = userReminder;
        } else {
          _waterReminder = null;
        }
        notifyListeners();
        return;
      }

      // Only update if there's a change
      if (_waterReminder == null ||
          _waterReminder!.lastUpdated != reminder.lastUpdated ||
          _waterReminder!.currentIntake != reminder.currentIntake ||
          _waterReminder!.targetIntake != reminder.targetIntake) {
        _waterReminder = reminder;
        notifyListeners();
      }
    } catch (e) {
      print('Error refreshing water reminder: $e');
      // Don't update error state on background refresh
    }
  }

  // Add water intake
  Future<void> addWaterIntake(int amount) async {
    try {
      final updatedReminder = await _service.updateWaterIntake(amount);
      _waterReminder = updatedReminder;
      notifyListeners();

      // Schedule background sync to ensure data is saved
      _scheduleBackgroundSync();
    } catch (e) {
      _error = 'Failed to update water intake: $e';
      notifyListeners();
    }
  }

  // Schedule background sync to ensure data persistence
  void _scheduleBackgroundSync() {
    // Cancel existing timer
    _refreshTimer?.cancel();

    // Schedule sync in 30 seconds to batch multiple updates
    _refreshTimer = Timer(const Duration(seconds: 30), () async {
      try {
        await refreshWaterReminder();
        debugPrint('💧 Background sync completed');
      } catch (e) {
        debugPrint('💧 Background sync failed: $e');
      }
    });
  }

  // Reset water intake
  Future<void> resetWaterIntake() async {
    try {
      final updatedReminder = await _service.resetWaterIntake();
      _waterReminder = updatedReminder;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to reset water intake: $e';
      notifyListeners();
    }
  }

  // Save user settings (when user configures their own settings)
  Future<void> saveUserSettings(WaterReminder reminder) async {
    try {
      await _service.saveUserSettings(reminder);
      _waterReminder = reminder;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save user settings: $e';
      notifyListeners();
      rethrow;
    }
  }

  // Save updated water reminder settings (interval, target, notifications)
  Future<void> saveWaterReminderSettings({
    required int interval,
    required int targetIntake,
    required bool isNotificationsEnabled,
  }) async {
    if (_waterReminder == null) {
      // If no reminder exists, create a basic one before saving settings
      await setupWaterReminder(targetIntake, interval);
    }

    final updatedReminder = _waterReminder!.copyWith(
      reminderInterval: interval,
      targetIntake: targetIntake,
      isNotificationsEnabled: isNotificationsEnabled,
      lastUpdated: DateTime.now(),
    );

    try {
      // Save settings to backend
      await _service.saveUserSettings(updatedReminder);
      _waterReminder = updatedReminder;
      
      // Handle notifications
      final notificationService = EnhancedNotificationService();
      if (isNotificationsEnabled) {
        // Schedule new notifications with updated interval
        await notificationService.scheduleWaterReminders(
          dailyTarget: targetIntake,
          intervalHours: interval,
        );
      } else {
        // Cancel all notifications if disabled
        await notificationService.cancelWaterReminders();
      }
      
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save water reminder settings: $e';
      notifyListeners();
      rethrow;
    }
  }

  // Setup water reminder from user input (goal and interval in hours)
  Future<void> setupWaterReminder(int goal, int intervalHours) async {
    // Save goal settings first
    await updateDailyGoal(goal);

    final now = DateTime.now();
    final reminder = WaterReminder(
      id: now.millisecondsSinceEpoch,
      title: 'Water Reminder',
      message: 'Time to drink water!',
      time: TimeOfDay(hour: now.hour, minute: now.minute),
      isEnabled: true,
      targetIntake: goal,
      currentIntake: 0,
      logs: [],
      lastUpdated: now,
      reminderInterval: intervalHours,
      isNotificationsEnabled: true,
    );
    await saveUserSettings(reminder);
    // Optionally, schedule notifications here if needed
    await updateNotificationInterval(intervalHours * 60);
  }

  // Update daily water goal
  Future<void> updateDailyGoal(int goalInMl) async {
    try {
      // Save goal settings first
      await _goalService.updateDailyGoal(goalInMl);
      await loadGoalSettings();

      // Update current reminder if it exists
      if (_waterReminder != null) {
        final updatedReminder = _waterReminder!.copyWith(
          targetIntake: goalInMl,
          lastUpdated: DateTime.now(),
        );

        // Update the local state immediately
        _waterReminder = updatedReminder;

        // Save to persistent storage
        await saveUserSettings(updatedReminder);

        // Notify listeners immediately for UI updates
        notifyListeners();

        debugPrint('💧 Goal updated: ${goalInMl}ml, Progress bar target updated');
      } else {
        // If no reminder exists, create one with the new goal
        final now = DateTime.now();
        final newReminder = WaterReminder(
          id: now.millisecondsSinceEpoch,
          title: 'Water Reminder',
          message: 'Time to drink water!',
          time: TimeOfDay(hour: now.hour, minute: now.minute),
          isEnabled: true,
          targetIntake: goalInMl,
          currentIntake: 0,
          logs: [],
          lastUpdated: now,
          reminderInterval: 2,
          isNotificationsEnabled: true,
        );

        _waterReminder = newReminder;
        await saveUserSettings(newReminder);
        notifyListeners();

        debugPrint('💧 New reminder created with goal: ${goalInMl}ml');
      }
    } catch (e) {
      _error = 'Failed to update daily goal: $e';
      notifyListeners();
      rethrow;
    }
  }

  // Update preferred unit
  Future<void> updatePreferredUnit(WaterUnit unit) async {
    try {
      await _goalService.updatePreferredUnit(unit);
      await loadGoalSettings();
    } catch (e) {
      _error = 'Failed to update preferred unit: $e';
      notifyListeners();
      rethrow;
    }
  }

  // Get goal in preferred unit
  double getGoalInPreferredUnit() {
    if (_goalSettings == null) return 2.0; // Default 2L
    return _goalSettings!.getGoalInUnit(_goalSettings!.preferredUnit);
  }

  // Get formatted goal display
  String getFormattedGoal() {
    if (_goalSettings == null) return '2L';
    return _goalSettings!.displayText;
  }

  // Update notification interval
  Future<void> updateNotificationInterval(int intervalMinutes) async {
    try {
      // Import the notification service
      final notificationService = EnhancedNotificationService();

      // Set the interval in hours (convert from minutes)
      final intervalHours = (intervalMinutes / 60).round().clamp(1, 24);
      await notificationService.setWaterReminderInterval(intervalHours);

      print('Updated water reminder interval to $intervalMinutes minutes ($intervalHours hours)');
    } catch (e) {
      print('Error updating notification interval: $e');
      // Don't throw error as this is not critical for saving settings
    }
  }

  // Get motivational message based on progress
  String getMotivationalMessage() {
    if (_waterReminder == null) return 'Stay hydrated!';

    final progress = _waterReminder!.progressPercentage;

    if (progress < 0.25) {
      return 'Start your day right! Drink some water now.';
    } else if (progress < 0.5) {
      return 'You\'re making progress! Keep drinking water.';
    } else if (progress < 0.75) {
      return 'More than halfway there! Keep it up!';
    } else if (progress < 1.0) {
      return 'Almost there! Just a bit more water to reach your goal.';
    } else {
      return 'Congratulations! You\'ve reached your water intake goal for today!';
    }
  }

  // Get water intake suggestion
  int getSuggestedIntake() {
    if (_waterReminder == null) return 250; // Default suggestion: 250ml

    // If user hasn't had any water yet, suggest a larger amount
    if (_waterReminder!.currentIntake == 0) {
      return 300; // Suggest 300ml to start the day
    }

    // If user is close to target, suggest the remaining amount
    final remaining = _waterReminder!.remainingIntake;
    if (remaining < 300) {
      return remaining;
    }

    // Otherwise suggest a standard glass of water (250ml)
    return 250;
  }

  // Get time until next reminder
  String getTimeUntilNextReminder() {
    if (_waterReminder == null) return 'Unknown';

    final nextReminder = _waterReminder!.getNextReminderTime();
    final now = DateTime.now();
    final difference = nextReminder.difference(now);

    final hours = difference.inHours;
    final minutes = difference.inMinutes % 60;

    if (hours > 0) {
      return '$hours hour${hours > 1 ? 's' : ''} and $minutes minute${minutes > 1 ? 's' : ''}';
    } else {
      return '$minutes minute${minutes > 1 ? 's' : ''}';
    }
  }

  // Initialize periodic checks
  void initializePeriodicChecks() {
    // Cancel existing timer if any
    _refreshTimer?.cancel();

    // Check every minute for midnight reset
    _refreshTimer = Timer.periodic(const Duration(minutes: 1), (timer) async {
      try {
        await _service.checkAndResetWaterIntake();
        // Refresh the reminder data after potential reset
        await refreshWaterReminder();
      } catch (e) {
        debugPrint('Error in periodic water check: $e');
      }
    });
  }

  set waterReminder(WaterReminder? reminder) {
    _waterReminder = reminder;
    notifyListeners();
  }

  // Public method to get the default goal in ml
  Future<int> getDefaultGoalInMl() async {
    final goalSettings = await _goalService.getGoalSettings();
    return goalSettings.goalInMl;
  }
}
