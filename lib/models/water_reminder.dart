import 'package:flutter/material.dart';

class WaterReminder {
  final int id;
  final String title;
  final String message;
  final TimeOfDay time;
  final bool isEnabled;
  final int targetIntake; // Target water intake in ml
  final int currentIntake; // Current water intake in ml
  final List<WaterLog> logs;
  final DateTime lastUpdated;
  final int reminderInterval;
  final bool isNotificationsEnabled;

  WaterReminder({
    required this.id,
    required this.title,
    required this.message,
    required this.time,
    required this.isEnabled,
    required this.targetIntake,
    required this.currentIntake,
    required this.logs,
    required this.lastUpdated,
    required this.reminderInterval,
    required this.isNotificationsEnabled,
  });

  factory WaterReminder.fromJson(Map<String, dynamic> json) {
    // Parse time from string (HH:MM format)
    final timeParts = (json['time'] as String).split(':');
    final hour = int.parse(timeParts[0]);
    final minute = int.parse(timeParts[1]);

    // Parse logs
    final logsList = (json['logs'] as List<dynamic>?)?.map((log) => WaterLog.fromJson(log)).toList() ?? [];

    return WaterReminder(
      id: json['id'],
      title: json['title'],
      message: json['message'],
      time: TimeOfDay(hour: hour, minute: minute),
      isEnabled: json['is_enabled'] ?? false,
      targetIntake: json['target_intake'] ?? 2000,
      currentIntake: json['current_intake'] ?? 0,
      logs: logsList,
      lastUpdated: json['last_updated'] != null 
          ? DateTime.parse(json['last_updated']) 
          : DateTime.now(),
      reminderInterval: json['reminder_interval'] ?? 2,
      isNotificationsEnabled: json['is_notifications_enabled'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'time': '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}',
      'is_enabled': isEnabled,
      'target_intake': targetIntake,
      'current_intake': currentIntake,
      'logs': logs.map((log) => log.toJson()).toList(),
      'last_updated': lastUpdated.toIso8601String(),
      'reminder_interval': reminderInterval,
      'is_notifications_enabled': isNotificationsEnabled,
    };
  }

  WaterReminder copyWith({
    int? id,
    String? title,
    String? message,
    TimeOfDay? time,
    bool? isEnabled,
    int? targetIntake,
    int? currentIntake,
    List<WaterLog>? logs,
    DateTime? lastUpdated,
    int? reminderInterval,
    bool? isNotificationsEnabled,
  }) {
    return WaterReminder(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      time: time ?? this.time,
      isEnabled: isEnabled ?? this.isEnabled,
      targetIntake: targetIntake ?? this.targetIntake,
      currentIntake: currentIntake ?? this.currentIntake,
      logs: logs ?? this.logs,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      reminderInterval: reminderInterval ?? this.reminderInterval,
      isNotificationsEnabled: isNotificationsEnabled ?? this.isNotificationsEnabled,
    );
  }

  // Calculate progress percentage
  double get progressPercentage {
    if (targetIntake == 0) return 0.0;
    return (currentIntake / targetIntake).clamp(0.0, 1.0);
  }

  // Get remaining intake
  int get remainingIntake {
    return (targetIntake - currentIntake).clamp(0, targetIntake);
  }

  // Get formatted time string
  String get formattedTime {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  // Get next reminder time
  DateTime getNextReminderTime() {
    final now = DateTime.now();
    final reminderTime = DateTime(
      now.year,
      now.month,
      now.day,
      time.hour,
      time.minute,
    );
    
    // If the reminder time is in the past, schedule for tomorrow
    if (reminderTime.isBefore(now)) {
      return reminderTime.add(const Duration(days: 1));
    }
    
    return reminderTime;
  }
}

class WaterLog {
  final int id;
  final int amount; // Amount in ml
  final DateTime timestamp;

  WaterLog({
    required this.id,
    required this.amount,
    required this.timestamp,
  });

  factory WaterLog.fromJson(Map<String, dynamic> json) {
    return WaterLog(
      id: json['id'],
      amount: json['amount'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  // Get formatted time
  String get formattedTime {
    return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
  }
}
