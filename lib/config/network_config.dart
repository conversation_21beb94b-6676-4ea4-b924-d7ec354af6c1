import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';
import 'package:http/http.dart' as http;

class NetworkConfig {
  // Development mode endpoints
  static const List<String> developmentEndpoints = [
    'http://localhost:9001/admin/api/',
    'http://127.0.0.1:9001/admin/api/',
    'https://mycloudforge.com/admin/api/',
    'http://***************:9001/admin/api/',
  ];

  // Production endpoint
  static const String productionEndpoint = 'https://mycloudforge.com/admin/api/';

  // Current endpoint index for development
  static int _currentEndpointIndex = 0;

  /// Get the current API endpoint
  static String getCurrentEndpoint() {
    if (kDebugMode) {
      return developmentEndpoints[_currentEndpointIndex];
    }
    return productionEndpoint;
  }

  /// Switch to next development endpoint
  static String switchToNextEndpoint() {
    if (kDebugMode && developmentEndpoints.isNotEmpty) {
      _currentEndpointIndex = (_currentEndpointIndex + 1) % developmentEndpoints.length;
      print('Switched to endpoint: ${developmentEndpoints[_currentEndpointIndex]}');
      return developmentEndpoints[_currentEndpointIndex];
    }
    return productionEndpoint;
  }

  /// Test connectivity to an endpoint
  static Future<bool> testEndpointConnectivity(String endpoint) async {
    try {
      // Use http package instead of HttpClient for web compatibility
      final response = await http.get(
        Uri.parse(endpoint),
        headers: {'User-Agent': 'KFT-Fitness-App/1.0'},
      ).timeout(const Duration(seconds: 10));

      print('Endpoint $endpoint responded with status: ${response.statusCode}');
      return response.statusCode >= 200 && response.statusCode < 500;
    } catch (e) {
      print('Endpoint $endpoint failed: $e');
      return false;
    }
  }

  /// Get cached endpoint from SharedPreferences
  static Future<String?> getCachedEndpoint() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('cached_api_endpoint');
    } catch (e) {
      print('Error getting cached endpoint: $e');
      return null;
    }
  }

  /// Cache the working endpoint
  static Future<void> cacheEndpoint(String endpoint) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('cached_api_endpoint', endpoint);
    } catch (e) {
      print('Error caching endpoint: $e');
    }
  }

  /// Find the first working endpoint
  static Future<String> findWorkingEndpoint() async {
    if (!kDebugMode) {
      return productionEndpoint;
    }

    print('Testing development endpoints for connectivity...');

    // In debug mode, prioritize local endpoints
    for (int i = 0; i < developmentEndpoints.length; i++) {
      final endpoint = developmentEndpoints[i];
      print('Testing endpoint: $endpoint');

      try {
        if (await testEndpointConnectivity(endpoint)) {
          _currentEndpointIndex = i;
          print('Found working endpoint: $endpoint');
          // Cache the working endpoint
          await cacheEndpoint(endpoint);
          return endpoint;
        }
      } catch (e) {
        print('Error testing endpoint $endpoint: $e');
        // Continue to next endpoint
      }
    }

    // If no endpoints work, default to localhost in debug mode
    print('No working development endpoints found, defaulting to localhost in debug mode');
    _currentEndpointIndex = 0; // Set to localhost
    await cacheEndpoint(developmentEndpoints[0]);
    return developmentEndpoints[0];
  }

  /// Get network error suggestions based on the error
  static String getNetworkErrorSuggestion(String error) {
    if (error.contains('Connection refused')) {
      return '''
Connection refused error suggestions:
1. Check if the server is running on the expected port
2. Verify the IP address is correct (current: ${getCurrentEndpoint()})
3. Check firewall settings on both device and server
4. For MIUI devices: Disable battery optimization for this app
5. Try switching to a different endpoint using the debug menu
''';
    } else if (error.contains('CERTIFICATE_VERIFY_FAILED')) {
      return '''
SSL Certificate error suggestions:
1. This is common in development with self-signed certificates
2. The app will automatically bypass SSL verification in debug mode
3. Ensure network_security_config.xml allows the domain
4. Check if the server has valid SSL certificates
''';
    } else if (error.contains('Network is unreachable')) {
      return '''
Network unreachable error suggestions:
1. Check your internet connection
2. Verify WiFi/mobile data is working
3. Try connecting to a different network
4. Check if the server domain is accessible from your network
''';
    } else {
      return '''
General network error suggestions:
1. Check your internet connection
2. Verify the server is accessible
3. Try switching to a different endpoint
4. Check firewall and security settings
''';
    }
  }

  /// Get current network status info
  static Map<String, dynamic> getNetworkStatus() {
    return {
      'current_endpoint': getCurrentEndpoint(),
      'is_debug_mode': kDebugMode,
      'available_endpoints': kDebugMode ? developmentEndpoints : [productionEndpoint],
      'current_index': _currentEndpointIndex,
    };
  }
}
