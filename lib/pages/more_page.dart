import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/user_service.dart';
import '../services/quote_service.dart';
import '../widgets/logout_button.dart';
import '../services/api_service.dart';
import '../services/progress_settings_service.dart';
import '../services/progress_service.dart';
import '../services/daily_streak_service.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../services/navigation_service.dart';
import 'package:provider/provider.dart';

class MorePage extends StatefulWidget {
  const MorePage({Key? key}) : super(key: key);

  @override
  _MorePageState createState() => _MorePageState();
}

class _MorePageState extends State<MorePage> {
  final UserService _userService = UserService();
  final QuoteService _quoteService = QuoteService();
  final ProgressSettingsService _progressSettingsService = ProgressSettingsService();
  bool _showQuotes = true;
  bool _isLoading = true;
  bool _achievementNotifications = true;
  bool _weeklyReports = true;
  bool _monthlyReports = true;

  @override
  void initState() {
    super.initState();
    _loadPreferences();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _loadPreferences() async {
    try {
      print('📱 MorePage: Starting to load preferences');
      final prefs = await SharedPreferences.getInstance();
      print('📱 MorePage: SharedPreferences loaded successfully');
      
      print('📱 MorePage: Initializing ProgressSettingsService');
      await _progressSettingsService.initialize();
      print('📱 MorePage: ProgressSettingsService initialized successfully');

      setState(() {
        _showQuotes = prefs.getBool('show_quotes') ?? true;
        _achievementNotifications = _progressSettingsService.achievementNotificationsEnabled;
        _weeklyReports = _progressSettingsService.weeklyReportsEnabled;
        _monthlyReports = _progressSettingsService.monthlyReportsEnabled;
        _isLoading = false;
      });
      print('📱 MorePage: Preferences loaded successfully');
    } catch (e) {
      print('❌ MorePage: Error loading preferences: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveQuotePreference(bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('show_quotes', value);
      setState(() {
        _showQuotes = value;
      });
    } catch (e) {
      print('Error saving quote preference: $e');
    }
  }

  Future<void> _logout() async {
    try {
      await _userService.logout();
      Navigator.of(context).pushReplacementNamed('/login');
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error logging out: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Show goal settings dialog
  Future<void> _showGoalSettingsDialog() async {
    final goalSettings = _progressSettingsService.goalSettings;

    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.flag, color: Colors.blue),
              SizedBox(width: 8),
              Text('Goal Settings'),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildGoalSlider(
                  'Daily Video Goal',
                  goalSettings['daily_video_goal'].toDouble(),
                  1.0,
                  10.0,
                  (value) async {
                    await _progressSettingsService.updateGoal('daily_video_goal', value.round());
                  },
                  'videos',
                ),
                const SizedBox(height: 16),
                _buildGoalSlider(
                  'Daily Minutes Goal',
                  goalSettings['daily_minutes_goal'].toDouble(),
                  10.0,
                  120.0,
                  (value) async {
                    await _progressSettingsService.updateGoal('daily_minutes_goal', value.round());
                  },
                  'minutes',
                ),
                const SizedBox(height: 16),
                _buildGoalSlider(
                  'Weekly Video Goal',
                  goalSettings['weekly_video_goal'].toDouble(),
                  3.0,
                  50.0,
                  (value) async {
                    await _progressSettingsService.updateGoal('weekly_video_goal', value.round());
                  },
                  'videos',
                ),
                const SizedBox(height: 16),
                _buildGoalSlider(
                  'Streak Goal',
                  goalSettings['streak_goal'].toDouble(),
                  3.0,
                  100.0,
                  (value) async {
                    await _progressSettingsService.updateGoal('streak_goal', value.round());
                  },
                  'days',
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Done'),
            ),
          ],
        );
      },
    );
  }

  /// Reset progress with confirmation
  Future<void> _resetProgress() async {
    final success = await _progressSettingsService.resetAllProgress(context: context);
    if (success) {
      _showSnackBar('All progress data has been reset successfully');
    }
  }

  /// Show snackbar helper
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final navigationService = Provider.of<NavigationService>(context, listen: false);
    final theme = Theme.of(context);

    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('More'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: ListView(
        children: [
          _buildSection(
            title: 'Account',
            icon: Icons.person_outline,
            children: [
              ListTile(
                leading: const Icon(Icons.account_circle_outlined),
                title: const Text('Full Profile'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => navigationService.pushNamed(NavigationService.profile),
              ),
              ListTile(
                leading: const Icon(Icons.bar_chart_outlined),
                title: const Text('View Fitness Stats'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => navigationService.pushNamed(NavigationService.progress),
              ),
              ListTile(
                leading: const Icon(Icons.settings_outlined),
                title: const Text('Settings'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => navigationService.pushNamed(NavigationService.settings),
              ),
              ListTile(
                leading: const Icon(Icons.notifications_active_outlined),
                title: const Text('Notification Settings'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => navigationService.pushNamed(NavigationService.notificationSettings),
              ),
            ],
          ),
          _buildSection(
            title: 'Health & Wellness',
            icon: Icons.favorite_outline,
            children: [
              ListTile(
                leading: const Icon(Icons.water_drop_outlined),
                title: const Text('Water Reminder'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => navigationService.pushNamed(NavigationService.waterReminder),
              ),
              ListTile(
                leading: const Icon(Icons.health_and_safety_outlined),
                title: const Text('System Health'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => navigationService.pushNamed(NavigationService.systemHealth),
              ),
            ],
          ),
          _buildSection(
            title: 'Your Progress',
            icon: Icons.show_chart_outlined,
            children: [
              ListTile(
                leading: const Icon(Icons.flag_outlined),
                title: const Text('Goal Settings'),
                subtitle: const Text('Set daily and weekly fitness goals'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => _showGoalSettingsDialog(),
              ),
              ListTile(
                leading: const Icon(Icons.refresh_outlined),
                title: const Text('Reset Progress'),
                subtitle: const Text('Clear all progress data'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => _resetProgress(),
              ),
            ],
          ),
          _buildSection(
            title: 'Progress Notifications',
            icon: Icons.notifications_outlined,
            children: [
              SwitchListTile(
                secondary: Icon(
                  _achievementNotifications ? Icons.emoji_events : Icons.emoji_events_outlined,
                  color: _achievementNotifications ? Colors.amber : theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                title: const Text('Achievement Notifications'),
                subtitle: const Text('Get notified when you reach milestones'),
                value: _achievementNotifications,
                onChanged: (value) async {
                  await _progressSettingsService.toggleAchievementNotifications(value);
                  setState(() {
                    _achievementNotifications = value;
                  });
                  _showSnackBar(value ? 'Achievement notifications enabled' : 'Achievement notifications disabled');
                },
              ),
              SwitchListTile(
                secondary: Icon(
                  _weeklyReports ? Icons.calendar_view_week : Icons.calendar_view_week_outlined,
                  color: _weeklyReports ? Colors.blue : theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                title: const Text('Weekly Progress Reports'),
                subtitle: const Text('Receive weekly activity summaries'),
                value: _weeklyReports,
                onChanged: (value) async {
                  await _progressSettingsService.toggleWeeklyReports(value);
                  setState(() {
                    _weeklyReports = value;
                  });
                  _showSnackBar(value ? 'Weekly reports enabled' : 'Weekly reports disabled');
                },
              ),
              SwitchListTile(
                secondary: Icon(
                  _monthlyReports ? Icons.calendar_view_month : Icons.calendar_view_month_outlined,
                  color: _monthlyReports ? Colors.green : theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                title: const Text('Monthly Progress Reports'),
                subtitle: const Text('Receive monthly progress insights'),
                value: _monthlyReports,
                onChanged: (value) async {
                  await _progressSettingsService.toggleMonthlyReports(value);
                  setState(() {
                    _monthlyReports = value;
                  });
                  _showSnackBar(value ? 'Monthly reports enabled' : 'Monthly reports disabled');
                },
              ),
            ],
          ),
          _buildSection(
            title: 'Motivational Quotes',
            icon: Icons.format_quote_outlined,
            children: [
              SwitchListTile(
                secondary: Icon(
                  _showQuotes ? Icons.format_quote : Icons.format_quote_outlined,
                  color: _showQuotes ? theme.colorScheme.primary : theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                title: const Text('Daily Motivational Quotes'),
                subtitle: const Text('Get personalized quotes based on your activity'),
                value: _showQuotes,
                onChanged: (value) {
                  _saveQuotePreference(value);

                  // Show confirmation
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(value
                        ? 'Motivational quotes enabled'
                        : 'Motivational quotes disabled'
                      ),
                      action: value ? SnackBarAction(
                        label: 'REFRESH',
                        onPressed: () async {
                          try {
                            await _quoteService.getQuote(forceRefresh: true);
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('Quote refreshed')),
                            );
                          } catch (e) {
                            print('Error refreshing quote: $e');
                          }
                        },
                      ) : null,
                    ),
                  );
                },
              ),
            ],
          ),
          _buildSection(
            title: 'Support',
            icon: Icons.support_agent_outlined,
            children: [
              FutureBuilder<Map<String, String>?>(
                future: _getStaffContactInfo(),
                builder: (context, snapshot) {
                  return ListTile(
                    leading: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFF25D366).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const FaIcon(
                        FontAwesomeIcons.whatsapp,
                        color: Color(0xFF25D366),
                        size: 20,
                      ),
                    ),
                    title: const Text('Contact Support'),
                    subtitle: const Text('Get help via WhatsApp'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _launchWhatsApp(snapshot.data),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build goal slider widget
  Widget _buildGoalSlider(
    String title,
    double currentValue,
    double min,
    double max,
    Function(double) onChanged,
    String unit,
  ) {
    return StatefulBuilder(
      builder: (context, setState) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$title: ${currentValue.round()} $unit',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            Slider(
              value: currentValue,
              min: min,
              max: max,
              divisions: (max - min).round(),
              onChanged: (value) {
                setState(() {
                  currentValue = value;
                });
                onChanged(value);
              },
            ),
          ],
        );
      },
    );
  }

  // Get staff contact information with improved error handling
  Future<Map<String, String>?> _getStaffContactInfo() async {
    try {
      final apiService = ApiService();
      final userService = UserService();

      // Get user profile with timeout and retry
      final userProfile = await _getUserProfileWithRetry(userService);

      if (userProfile?.assignedStaffId != null && userProfile!.assignedStaffId! > 0) {
        // Get assigned staff info with timeout
        final staffInfo = await apiService.getStaffInfo(userProfile.assignedStaffId!);
        if (staffInfo != null && staffInfo['phone']?.isNotEmpty == true) {
          return staffInfo;
        }
      }

      // Fallback to default support number
      return {
        'name': 'Support Team',
        'phone': '+91 96564 44498', // Default support number
      };
    } catch (e) {
      print('Error getting staff contact info: $e');
      // Return default support number on error
      return {
        'name': 'Support Team',
        'phone': '+91 96564 44498',
      };
    }
  }

  /// Get user profile with retry mechanism
  Future<dynamic> _getUserProfileWithRetry(UserService userService, {int maxRetries = 3}) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Add timeout to prevent hanging
        final profile = await userService.getUserProfile().timeout(
          const Duration(seconds: 10),
          onTimeout: () => throw TimeoutException('Profile loading timeout', const Duration(seconds: 10)),
        );
        return profile;
      } catch (e) {
        print('Profile loading attempt $attempt failed: $e');
        if (attempt == maxRetries) {
          return null; // Return null after all retries failed
        }
        // Wait before retry with exponential backoff
        await Future.delayed(Duration(seconds: attempt * 2));
      }
    }
    return null;
  }

  // Launch WhatsApp with staff contact
  Future<void> _launchWhatsApp(Map<String, String>? staffInfo) async {
    try {
      String phone = staffInfo?['phone'] ?? '+91 96564 44498';
      String staffName = staffInfo?['name'] ?? 'Support Team';

      // Clean and format phone number
      phone = phone.replaceAll(RegExp(r'[^\d+]'), '');
      if (!phone.startsWith('+')) {
        phone = '+$phone';
      }

      // Create WhatsApp URL
      final url = 'https://wa.me/${phone.replaceAll('+', '')}';

      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Could not open WhatsApp. Contact $staffName at $phone'),
              duration: const Duration(seconds: 4),
            ),
          );
        }
      }
    } catch (e) {
      print('Error launching WhatsApp: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open WhatsApp. Please try again.'),
          ),
        );
      }
    }
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Row(
            children: [
              Icon(icon, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        ...children,
        const Divider(),
      ],
    );
  }
}
