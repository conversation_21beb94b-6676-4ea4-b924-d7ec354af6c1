import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/water_reminder_provider.dart';
import '../models/water_reminder.dart';
import '../design_system/kft_design_system.dart';
import '../widgets/water_goal_settings_widget.dart';
import '../services/awesome_notification_service.dart';

class WaterReminderPage extends StatefulWidget {
  const WaterReminderPage({Key? key}) : super(key: key);

  @override
  State<WaterReminderPage> createState() => _WaterReminderPageState();
}

class _WaterReminderPageState extends State<WaterReminderPage> {
  final List<int> _quickAddOptions = [50, 100, 150, 200];
  final TextEditingController _customAmountController = TextEditingController();
  final _notificationService = EnhancedNotificationService();
  
  @override
  void initState() {
    super.initState();
    // Refresh water reminder data when page opens
    Future.microtask(() {
      Provider.of<WaterReminderProvider>(context, listen: false).refreshWaterReminder();
    });
  }
  
  @override
  void dispose() {
    _customAmountController.dispose();
    super.dispose();
  }

  // Handle notification permission request
  Future<void> _handleNotificationPermission(BuildContext context, WaterReminderProvider provider) async {
    final hasPermission = await _notificationService.requestPermissionsWithExplanation(context);
    
    if (hasPermission) {
      // Update reminder settings with notifications enabled
      if (provider.waterReminder != null) {
        await provider.saveWaterReminderSettings(
          interval: provider.waterReminder!.reminderInterval,
          targetIntake: provider.waterReminder!.targetIntake,
          isNotificationsEnabled: true,
        );
      }
    } else {
      // Show message if permission denied
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notifications are required for water reminders. Please enable them in settings.'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Water Reminder'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Consumer<WaterReminderProvider>(
        builder: (context, provider, _) {
          if (provider.isLoading && provider.waterReminder == null) {
            return const Center(child: CircularProgressIndicator());
          }
          
          if (provider.error != null && provider.waterReminder == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 48, color: Colors.red.shade400),
                  const SizedBox(height: 16),
                  Text(
                    'Error: ${provider.error}',
                    style: TextStyle(color: Colors.red.shade700),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => provider.loadWaterReminder(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }
          
          final reminder = provider.waterReminder;
          if (reminder == null) {
            return const Center(child: Text('No water reminder data available'));
          }
          
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildWaterProgressCard(context, reminder, provider),
                const SizedBox(height: 24),
                _buildQuickAddSection(context, provider),
                const SizedBox(height: 24),
                _buildWaterLogSection(context, reminder),
              ],
            ),
          );
        },
      ),
    );
  }
  
  Widget _buildWaterProgressCard(
    BuildContext context, 
    WaterReminder reminder,
    WaterReminderProvider provider,
  ) {
    final theme = Theme.of(context);

    // Use current goal from goal settings if available
    final goalSettings = provider.goalSettings;
    final currentTarget = goalSettings?.goalInMl ?? reminder.targetIntake;
    final progress = currentTarget > 0 ? (reminder.currentIntake / currentTarget).clamp(0.0, 1.0) : 0.0;

    final motivationalMessage = provider.getMotivationalMessage();
    
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.water_drop,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Daily Water Intake',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.settings),
                  onPressed: () {
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      backgroundColor: Colors.transparent,
                      builder: (context) => const WaterGoalSettingsWidget(showAsBottomSheet: true),
                    );
                  },
                  tooltip: 'Goal Settings',
                ),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () => provider.refreshWaterReminder(),
                  tooltip: 'Refresh',
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: progress,
              minHeight: 12,
              backgroundColor: Colors.blue.shade100,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade500),
              borderRadius: BorderRadius.circular(6),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                      'Current Intake',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
                Text(
                      '${reminder.currentIntake}ml',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Daily Goal',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
              ),
                    Text(
                      '${currentTarget}ml',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
                  ),
            const SizedBox(height: 16),
            Text(
                      motivationalMessage,
                      style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
              const SizedBox(height: 16),
            if (!reminder.isNotificationsEnabled)
              ElevatedButton.icon(
                onPressed: () => _handleNotificationPermission(context, provider),
                icon: const Icon(Icons.notifications),
                label: const Text('Enable Notifications'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: Colors.white,
              ),
              ),
            if (reminder.isNotificationsEnabled)
            Text(
              'Next reminder in ${provider.getTimeUntilNextReminder()}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildQuickAddSection(
    BuildContext context,
    WaterReminderProvider provider,
  ) {
    final theme = Theme.of(context);
    final suggestedIntake = provider.getSuggestedIntake();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Add Water',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            for (final amount in _quickAddOptions)
              _buildQuickAddButton(
                context, 
                amount, 
                isRecommended: amount == suggestedIntake,
                onTap: () => provider.addWaterIntake(amount),
              ),
            _buildCustomAddButton(context, provider),
          ],
        ),
      ],
    );
  }
  
  Widget _buildQuickAddButton(
    BuildContext context, 
    int amount, {
    bool isRecommended = false,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isRecommended 
              ? theme.colorScheme.primary 
              : theme.colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: isRecommended
              ? null
              : Border.all(color: theme.colorScheme.primary.withOpacity(0.3)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.water_drop,
                  size: 16,
                  color: isRecommended 
                      ? Colors.white 
                      : theme.colorScheme.primary,
                ),
                const SizedBox(width: 4),
                Text(
                  '$amount ml',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: isRecommended 
                        ? Colors.white 
                        : theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
            if (isRecommended) ...[
              const SizedBox(height: 4),
              Text(
                'Recommended',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildCustomAddButton(
    BuildContext context,
    WaterReminderProvider provider,
  ) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: () => _showCustomAmountDialog(context, provider),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: theme.colorScheme.secondary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: theme.colorScheme.secondary.withOpacity(0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.add,
              size: 16,
              color: theme.colorScheme.secondary,
            ),
            const SizedBox(width: 4),
            Text(
              'Custom',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.secondary,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  void _showCustomAmountDialog(
    BuildContext context,
    WaterReminderProvider provider,
  ) {
    final theme = Theme.of(context);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Custom Amount'),
        content: TextField(
          controller: _customAmountController,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'Amount (ml)',
            hintText: 'Enter amount in ml',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final amount = int.tryParse(_customAmountController.text);
              if (amount != null && amount > 0) {
                provider.addWaterIntake(amount);
                _customAmountController.clear();
                Navigator.pop(context);
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }
  
  Widget _buildWaterLogSection(
    BuildContext context,
    WaterReminder reminder,
  ) {
    final theme = Theme.of(context);
    final logs = reminder.logs;
    
    if (logs.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Today\'s Water Log',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Center(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  Icon(
                    Icons.water_drop_outlined,
                    size: 48,
                    color: theme.colorScheme.primary.withOpacity(0.5),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No water intake logged today',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Add water using the quick add buttons above',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.5),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      );
    }
    
    // Sort logs by timestamp (newest first)
    final sortedLogs = List<WaterLog>.from(logs)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Today\'s Water Log',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: sortedLogs.length,
          separatorBuilder: (context, index) => const Divider(),
          itemBuilder: (context, index) {
            final log = sortedLogs[index];
            return ListTile(
              contentPadding: EdgeInsets.zero,
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.water_drop,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
              ),
              title: Text(
                '${log.amount} ml',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: Text(
                'Added at ${log.formattedTime}',
                style: TextStyle(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                  fontSize: 12,
                ),
              ),
              trailing: Text(
                _getTimeAgo(log.timestamp),
                style: TextStyle(
                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                  fontSize: 12,
                ),
              ),
            );
          },
        ),
      ],
    );
  }
  
  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} min ago';
    } else {
      return '${difference.inHours} hr ago';
    }
  }
}
