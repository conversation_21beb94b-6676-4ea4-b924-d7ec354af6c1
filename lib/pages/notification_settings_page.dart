import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/awesome_notification_service.dart';
import '../services/workout_video_notification_service.dart';
import '../widgets/kft_app_bar.dart';
import 'notification_setup_guide_page.dart';
import '../providers/notification_settings_riverpod_provider.dart';

class NotificationSettingsPage extends ConsumerStatefulWidget {
  const NotificationSettingsPage({super.key});

  @override
  ConsumerState<NotificationSettingsPage> createState() => _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends ConsumerState<NotificationSettingsPage> {
  TimeOfDay _selectedTime = TimeOfDay.now();
  TextEditingController _timeController = TextEditingController();
  List<TimeOfDay> _customTimes = [];

  @override
  void initState() {
    super.initState();
    _initializeTimeController();
    _initializeCustomTimes();
  }

  void _initializeTimeController() {
    final settings = ref.read(notificationSettingsProvider);
    if (settings.workoutTime.isNotEmpty) {
      final parts = settings.workoutTime.split(':');
      if (parts.length == 2) {
        _selectedTime = TimeOfDay(
          hour: int.parse(parts[0]),
          minute: int.parse(parts[1]),
        );
        // Use a simple 24-hour format for initialization
        _timeController.text = '${_selectedTime.hour.toString().padLeft(2, '0')}:${_selectedTime.minute.toString().padLeft(2, '0')}';
      }
    }
  }

  Future<void> _initializeCustomTimes() async {
    final service = EnhancedNotificationService();
    final customTimes = await service.getCustomWorkoutTimes();
    setState(() {
      _customTimes = customTimes.map((e) {
        final parts = (e['time'] as String).split(':');
        return TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
      }).toList();
    });
  }

  Future<void> _addCustomTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    if (picked != null) {
      setState(() {
        _customTimes.add(picked);
      });
      await _saveCustomTimes();
    }
  }

  Future<void> _removeCustomTime(int index) async {
    setState(() {
      _customTimes.removeAt(index);
    });
    await _saveCustomTimes();
  }

  Future<void> _saveCustomTimes() async {
    final service = EnhancedNotificationService();
    final times = _customTimes.map((t) => {
      'time': '${t.hour.toString().padLeft(2, '0')}:${t.minute.toString().padLeft(2, '0')}',
      'type': 'Custom',
      'days': [1,2,3,4,5,6,7],
      'enabled': true,
    }).toList();
    await service.setCustomWorkoutTimes(times);
  }

  @override
  void dispose() {
    _timeController.dispose();
    super.dispose();
  }

  void _showSuccessSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).clearSnackBars();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).clearSnackBars();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  Future<void> _requestPermissions() async {
    final notifier = ref.read(notificationSettingsProvider.notifier);
    await notifier.requestPermissions();
    if (ref.read(notificationSettingsProvider).hasPermissions) {
      _showSuccessSnackBar('Notification permissions granted!');
    } else {
      _showErrorSnackBar('Notification permissions denied. Some features may not work.');
    }
  }

  Future<void> _toggleWorkoutNotifications(bool enabled) async {
    final notifier = ref.read(notificationSettingsProvider.notifier);
    await notifier.setWorkoutNotificationsEnabled(enabled);
    if (ref.read(notificationSettingsProvider).error == null) {
      _showSuccessSnackBar(enabled ? 'Workout reminders enabled' : 'Workout reminders disabled');
    } else {
      _showErrorSnackBar(ref.read(notificationSettingsProvider).error!);
    }
  }

  Future<void> _selectWorkoutTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
              );
    if (picked != null && picked != _selectedTime) {
      setState(() {
        _selectedTime = picked;
        _timeController.text = _formatTimeOfDay(picked);
      });
      final notifier = ref.read(notificationSettingsProvider.notifier);
      await notifier.setWorkoutReminderTime(_formatTimeOfDay(picked));
      if (ref.read(notificationSettingsProvider).error == null) {
        _showSuccessSnackBar('Workout reminder time set to ${_formatTimeOfDay(picked)}');
      } else {
        _showErrorSnackBar(ref.read(notificationSettingsProvider).error!);
      }
    }
  }

  Future<void> _toggleVideoNotifications(bool enabled) async {
    final notifier = ref.read(notificationSettingsProvider.notifier);
    await notifier.setVideoNotificationsEnabled(enabled);
    if (ref.read(notificationSettingsProvider).error == null) {
      _showSuccessSnackBar(enabled ? 'Video notifications enabled' : 'Video notifications disabled');
    } else {
      _showErrorSnackBar(ref.read(notificationSettingsProvider).error!);
    }
  }

  Future<void> _toggleNewVideoNotifications(bool enabled) async {
    final notifier = ref.read(notificationSettingsProvider.notifier);
    await notifier.setNewVideoNotificationsEnabled(enabled);
    if (ref.read(notificationSettingsProvider).error == null) {
      _showSuccessSnackBar(enabled ? 'New video notifications enabled' : 'New video notifications disabled');
    } else {
      _showErrorSnackBar(ref.read(notificationSettingsProvider).error!);
    }
  }

  Future<void> _toggleUnlockNotifications(bool enabled) async {
    final notifier = ref.read(notificationSettingsProvider.notifier);
    await notifier.setUnlockNotificationsEnabled(enabled);
    if (ref.read(notificationSettingsProvider).error == null) {
      _showSuccessSnackBar(enabled ? 'Unlock notifications enabled' : 'Unlock notifications disabled');
    } else {
      _showErrorSnackBar(ref.read(notificationSettingsProvider).error!);
    }
  }

  Future<void> _toggleSmartSuggestions(bool enabled) async {
    final notifier = ref.read(notificationSettingsProvider.notifier);
    await notifier.setSmartSuggestionsEnabled(enabled);
    if (ref.read(notificationSettingsProvider).error == null) {
      _showSuccessSnackBar(enabled ? 'Smart suggestions enabled' : 'Smart suggestions disabled');
    } else {
      _showErrorSnackBar(ref.read(notificationSettingsProvider).error!);
    }
  }

  String _formatTimeOfDay(TimeOfDay time) {
    if (!mounted) {
      // Fallback to 24-hour format if widget is not mounted
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    }
    
    final is24Hour = MediaQuery.of(context).alwaysUse24HourFormat;
    if (is24Hour) {
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    } else {
      return time.format(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final settings = ref.watch(notificationSettingsProvider);

    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF0A0A0A) : const Color(0xFFF8F9FA),
      appBar: const KFTAppBar(
        title: 'Notifications',
        showBackButton: true,
      ),
      body: settings.isLoading
        ? Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const CircularProgressIndicator(),
                ),
                const SizedBox(height: 16),
                Text(
                  'Loading notification settings...',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          )
        : CustomScrollView(
            slivers: [
              SliverPadding(
                padding: const EdgeInsets.all(20),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    // Permission Status Card
                      _buildPermissionStatusCard(theme, isDark, settings.hasPermissions),
                    const SizedBox(height: 24),

                    // Workout Reminders Section
                    _buildSectionHeader('Workout Reminders', Icons.fitness_center, theme),
                    const SizedBox(height: 12),
                      _buildWorkoutReminderCard(
                          theme, isDark, settings.workoutNotificationsEnabled, settings.workoutTime, _selectWorkoutTime, _toggleWorkoutNotifications),
                    const SizedBox(height: 24),

                    // Workout Video Notifications Section
                    _buildSectionHeader('Workout Videos', Icons.video_library, theme),
                    const SizedBox(height: 12),
                      _buildVideoNotificationCard(
                          theme, isDark, settings.videoNotificationsEnabled, settings.newVideoNotificationsEnabled, settings.unlockNotificationsEnabled, settings.smartSuggestionsEnabled,
                          _toggleVideoNotifications, _toggleNewVideoNotifications, _toggleUnlockNotifications, _toggleSmartSuggestions),
                    const SizedBox(height: 24),

                    // Information Card
                    _buildInfoCard(theme, isDark),
                    const SizedBox(height: 100), // Bottom padding
                  ]),
                ),
              ),
            ],
          ),
    );
  }

  Widget _buildPermissionStatusCard(ThemeData theme, bool isDark, bool hasPermissions) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: isDark ? const Color(0xFF1C1C1C) : Colors.white,
      child: Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                    color: hasPermissions
                    ? const Color(0xFF4CAF50).withOpacity(0.2)
                    : const Color(0xFFFF9800).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                    hasPermissions ? Icons.verified_user : Icons.warning_rounded,
                    color: hasPermissions ? const Color(0xFF4CAF50) : const Color(0xFFFF9800),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Notification Permissions',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                        hasPermissions
                        ? 'All set! Notifications are working perfectly.'
                        : 'Permissions needed for reminders to work.',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
            if (!hasPermissions) ...[
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _requestPermissions,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFF9800),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                icon: const Icon(Icons.notifications_active),
                label: const Text(
                  'Enable Notifications',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ],
        ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, ThemeData theme) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: theme.colorScheme.primary,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildWorkoutReminderCard(
    ThemeData theme,
    bool isDark,
    bool workoutNotificationsEnabled,
    String workoutTime,
    VoidCallback onSelectTime,
    Function(bool) onToggleNotifications,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: isDark ? const Color(0xFF1C1C1C) : Colors.white,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    'Enable Workout Reminders',
                    style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                  ),
                ),
                Switch(
                  value: workoutNotificationsEnabled,
                  onChanged: onToggleNotifications,
                ),
              ],
            ),
          ),
          if (workoutNotificationsEnabled) ...[
            Container(
              width: double.infinity,
              height: 1,
              color: theme.colorScheme.outline.withOpacity(0.1),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Custom Workout Times',
                    style: theme.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  ..._customTimes.asMap().entries.map((entry) {
                    final index = entry.key;
                    final time = entry.value;
                    return Row(
                      children: [
                        Expanded(
                          child: Text(
                            _formatTimeOfDay(time),
                            style: theme.textTheme.bodyLarge,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red),
                          onPressed: () => _removeCustomTime(index),
                        ),
                      ],
                    );
                  }).toList(),
                  const SizedBox(height: 8),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: ElevatedButton.icon(
                      onPressed: _addCustomTime,
                      icon: const Icon(Icons.add, size: 20),
                      label: const Text('Add Custom Time'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildVideoNotificationCard(
    ThemeData theme,
    bool isDark,
    bool videoNotificationsEnabled,
    bool newVideoNotificationsEnabled,
    bool unlockNotificationsEnabled,
    bool smartSuggestionsEnabled,
    Function(bool) onToggleVideoNotifications,
    Function(bool) onToggleNewVideoNotifications,
    Function(bool) onToggleUnlockNotifications,
    Function(bool) onToggleSmartSuggestions,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: isDark ? const Color(0xFF1C1C1C) : Colors.white,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    'Enable Video Notifications',
                    style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                  ),
                ),
                Switch(
                  value: videoNotificationsEnabled,
                  onChanged: onToggleVideoNotifications,
                ),
              ],
            ),
          ),
          if (videoNotificationsEnabled) ...[
            Container(
              width: double.infinity,
              height: 1,
              color: theme.colorScheme.outline.withOpacity(0.1),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // New video notifications
                  _buildVideoNotificationOption(
                    theme,
                    'New Video Releases',
                    'Get notified when new workout videos are published',
                    Icons.new_releases,
                    newVideoNotificationsEnabled,
                    onToggleNewVideoNotifications,
                  ),
                  const SizedBox(height: 16),

                  // Unlock notifications
                  _buildVideoNotificationOption(
                    theme,
                    'Video Unlock Alerts',
                    'Receive notifications when requested videos unlock',
                    Icons.lock_open,
                    unlockNotificationsEnabled,
                    onToggleUnlockNotifications,
                  ),
                  const SizedBox(height: 16),

                  // Smart suggestions
                  _buildVideoNotificationOption(
                    theme,
                    'Smart Workout Suggestions',
                    'Get personalized workout time recommendations',
                    Icons.psychology,
                    smartSuggestionsEnabled,
                    onToggleSmartSuggestions,
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildVideoNotificationOption(
    ThemeData theme,
    String title,
    String description,
    IconData icon,
    bool value,
    Function(bool) onChanged,
  ) {
    final isEnabled = ref.watch(notificationSettingsProvider).videoNotificationsEnabled; // Check overall video notification status
    final opacity = isEnabled ? 1.0 : 0.5;

    return Opacity(
      opacity: opacity,
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF8B5CF6).withOpacity(isEnabled ? 0.1 : 0.05),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: const Color(0xFF8B5CF6).withOpacity(isEnabled ? 1.0 : 0.5),
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface.withOpacity(isEnabled ? 1.0 : 0.5),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(isEnabled ? 0.6 : 0.3),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Switch(
            value: value,
            onChanged: isEnabled ? onChanged : null, // Disable if main toggle is off
            activeColor: const Color(0xFF8B5CF6),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(ThemeData theme, bool isDark) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: isDark ? const Color(0xFF1C1C1C) : Colors.white,
      child: Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                  padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                    color: theme.colorScheme.secondary.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                    Icons.info_outline,
                    color: theme.colorScheme.secondary,
                    size: 24,
                ),
              ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Tips for Notifications',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                    ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
            _buildInfoItem(
              Icons.battery_alert,
              'Battery Optimization',
              'Ensure app is not optimized to receive timely notifications.',
                  theme,
          ),
          const SizedBox(height: 12),
            _buildInfoItem(
              Icons.settings_applications,
              'App Settings',
              "Manually adjust notification settings in your device's app settings.",
                  theme,
              ),
            ],
          ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String title, String description, ThemeData theme) {
    return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
        Icon(icon, color: theme.colorScheme.onSurface.withOpacity(0.7), size: 20),
        const SizedBox(width: 12),
          Expanded(
          child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
              const SizedBox(height: 2),
                    Text(
                description,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
        ),
      ],
    );
  }
}
