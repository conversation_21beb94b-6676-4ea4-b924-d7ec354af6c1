import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:url_launcher/url_launcher.dart';
// Conditional import for web-only video player
import 'video_player_web.dart' if (dart.library.io) 'video_player_stub.dart';
import '../models/course_video.dart';
import '../models/course.dart';
import '../services/api_service.dart';
import '../services/course_tracking_service.dart';
import '../services/progress_service.dart';
import '../widgets/custom_vimeo_player.dart';
import '../widgets/enhanced_vimeo_player.dart';
import 'dedicated_video_player_page.dart';
import '../models/user_profile.dart';
import '../utils/video_security_helper.dart';
import '../widgets/simple_vimeo_player.dart';
import '../services/global_notification_manager.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'offline_video_player_page.dart';

class VideoPlayerPage extends StatefulWidget {
  final CourseVideo video;
  final Course? course; // Optional course parameter

  const VideoPlayerPage({
    Key? key,
    required this.video,
    this.course,
  }) : super(key: key);

  @override
  _VideoPlayerPageState createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  final ApiService _apiService = ApiService();
  final CourseTrackingService _trackingService = CourseTrackingService();
  bool _isLoading = false;
  String _errorMessage = '';
  bool _isCompleted = false;

  @override
  void initState() {
    super.initState();
    _isCompleted = widget.video.isCompleted;

    // Notify global notification manager that video player is active
    GlobalNotificationManager().setVideoPlayerActive(true);

    // Track video progress
    _simulateWatchingVideo();

    // Track this video as the last accessed video
    _trackVideoAccess();
  }

  @override
  void dispose() {
    // Notify global notification manager that video player is no longer active
    GlobalNotificationManager().setVideoPlayerActive(false);
    super.dispose();
  }

  Future<void> _trackVideoAccess() async {
    try {
      // If we have a course, track it along with the video
      if (widget.course != null) {
        await _trackingService.trackCourseAndVideo(widget.course!, widget.video);
      } else {
        // Otherwise, just track the video
        await _trackingService.saveLastAccessedVideo(widget.video);
      }
    } catch (e) {
      print('Error tracking video access: $e');
    }
  }

  Future<void> _simulateWatchingVideo() async {
    // In a real app, you would track actual video progress
    // For this example, we'll just update the progress after a delay
    await Future.delayed(const Duration(seconds: 2));

    try {
      await _apiService.updateVideoProgress(
        videoId: widget.video.id,
        watchDurationSeconds: 30, // Simulate watching 30 seconds
        lastPositionSeconds: 30,
      );
    } catch (e) {
      setState(() {
        _errorMessage = e.toString().replaceAll('Exception: ', '');
      });
    }
  }

  Future<void> _updateVideoProgressSecure(int position) async {
    try {
      final progressService = ProgressService();
      await progressService.updateVideoProgress(
        videoId: widget.video.id,
        courseId: widget.course?.id,
        watchDurationSeconds: position,
        lastPositionSeconds: position,
        totalDurationSeconds: widget.video.durationMinutes != null ? widget.video.durationMinutes! * 60 : null,
      );
      debugPrint('✅ Video progress updated - Position: $position');
    } catch (e) {
      debugPrint('❌ Failed to update video progress: $e');
      // Don't rethrow to avoid disrupting playback
    }
  }

  Future<void> _markAsCompleted() async {
    if (_isCompleted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final progressService = ProgressService();
      final durationSeconds = widget.video.durationMinutes != null ? widget.video.durationMinutes! * 60 : 0;
      await progressService.updateVideoProgress(
        videoId: widget.video.id,
        courseId: widget.course?.id,
        isCompleted: true,
        totalDurationSeconds: durationSeconds,
        watchDurationSeconds: durationSeconds,
      );

      setState(() {
        _isCompleted = true;
        _isLoading = false;
      });

      // Update the video object to reflect completion
      final updatedVideo = CourseVideo(
        id: widget.video.id,
        title: widget.video.title,
        description: widget.video.description,
        videoUrl: widget.video.videoUrl,
        thumbnailUrl: widget.video.thumbnailUrl,
        durationMinutes: widget.video.durationMinutes,
        weekNumber: widget.video.weekNumber,
        sequenceNumber: widget.video.sequenceNumber,
        isUnlocked: widget.video.isUnlocked,
        isCompleted: true,
        unlockDate: widget.video.unlockDate,
        completionDate: DateTime.now().toIso8601String(),
        watchDurationSeconds: durationSeconds,
        lastPositionSeconds: durationSeconds,
        videoProvider: widget.video.videoProvider,
        videoEmbedUrl: widget.video.videoEmbedUrl,
        videoId: widget.video.videoId,
      );

      // Track the updated video
      if (widget.course != null) {
        await _trackingService.trackCourseAndVideo(widget.course!, updatedVideo);
      } else {
        await _trackingService.saveLastAccessedVideo(updatedVideo);
      }

      debugPrint('✅ Video marked as completed');
    } catch (e) {
      setState(() {
        _errorMessage = e.toString().replaceAll('Exception: ', '');
        _isLoading = false;
      });
      debugPrint('❌ Failed to mark video as completed: $e');
    }
  }

  Future<void> _logVideoCompletion() async {
    try {
      final response = await _apiService.makeApiRequest('profile.php');
      final userId = response['profile']?['id'] ?? 1;

      await VideoSecurityHelper.logVideoAccess(
        vimeoId: VideoSecurityHelper.extractVimeoId(widget.video.videoUrl) ?? '',
        videoId: widget.video.id,
        userId: userId,
        action: 'complete',
      );
    } catch (e) {
      // Do NOT clear tokens or logout on video logging errors
      debugPrint('Failed to log video completion: $e');
    }
  }

  // Helper method to open URLs
  Future<void> _openUrl(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
        setState(() {
          _errorMessage = 'Could not launch $url';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error opening URL: $e';
      });
    }
  }

  // Build the appropriate video player based on the video provider
  Widget _buildVideoPlayer() {
    // For Vimeo videos - use CustomVimeoPlayer everywhere
    if (widget.video.videoProvider == 'vimeo') {
      return AspectRatio(
        aspectRatio: 16 / 9,
        child: SimpleVimeoPlayer(
          video: widget.video,
        ),
      );
    }

    // For other video types, use the existing implementation
    if (widget.video.videoEmbedUrl != null) {
      if (kIsWeb) {
        // Use iframe for web platform
        return _buildVimeoIframePlayer();
      } else {
        // Use in-app player for mobile/desktop
        return _buildVimeoNativePlayer();
      }
    }

    // Fallback for other video types or when embed URL is not available
    return _buildVideoPreview();
  }

  // Build an iframe player for Vimeo videos on web
  Widget _buildVimeoIframePlayer() {
    if (!kIsWeb) {
      return _buildVideoPreview();
    }
    return buildVimeoIframePlayerWeb(widget.video, setState, _buildVideoPreview);
  }

  // Build a native player for Vimeo videos (mobile/desktop)
  Widget _buildVimeoNativePlayer() {
    try {
      return AspectRatio(
        aspectRatio: 16 / 9,
        child: // TODO: Replace with actual player implementation
        _buildVideoPreview(),
      );
    } catch (e) {
      // Fallback to preview if player fails
      return _buildVideoPreview();
    }
  }

  // Build a video preview with a play button
  Widget _buildVideoPreview() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.play_circle_outline,
              color: Colors.white,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              widget.video.videoProvider == 'vimeo'
                  ? 'Vimeo Player'
                  : 'Video Player',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              icon: const Icon(Icons.play_arrow),
              label: const Text('Play Video'),
              onPressed: () {
                // Open the video URL in a new tab or external app
                final url = widget.video.videoEmbedUrl ?? widget.video.videoUrl;
                _openUrl(url);
              },
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
            ),
            const SizedBox(height: 8),
            if (_errorMessage.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  _errorMessage,
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Add this method to handle download and encryption
  Future<void> _downloadAndEncryptVideo() async {
    final url = widget.video.videoUrl;
    final videoId = widget.video.videoId?.toString() ?? widget.video.id.toString();
    final storage = const FlutterSecureStorage();
    final keyString = await storage.read(key: 'video_key_$videoId') ?? encrypt.Key.fromSecureRandom(32).base64;
    await storage.write(key: 'video_key_$videoId', value: keyString);
    final key = encrypt.Key.fromBase64(keyString);
    final iv = encrypt.IV.fromLength(16);
    final encrypter = encrypt.Encrypter(encrypt.AES(key));

    try {
      final httpClient = HttpClient();
      final request = await httpClient.getUrl(Uri.parse(url));
      final response = await request.close();
      if (response.statusCode == 200) {
        final bytes = await consolidateHttpClientResponseBytes(response);
        final encrypted = encrypter.encryptBytes(bytes, iv: iv);
        final dir = await getApplicationDocumentsDirectory();
        final file = File('${dir.path}/video_$videoId.aes');
        await file.writeAsBytes(encrypted.bytes);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Video downloaded and encrypted for offline use!')),
        );
      } else {
        throw Exception('Failed to download video');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Download failed: $e')),
      );
    }
  }

  // Add this method to check for offline video and play it
  Future<bool> _tryPlayOfflineVideo() async {
    final videoId = widget.video.videoId?.toString() ?? widget.video.id.toString();
    final dir = await getApplicationDocumentsDirectory();
    final file = File('${dir.path}/video_$videoId.aes');
    if (await file.exists()) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (_) => OfflineVideoPlayerPage(videoId: videoId),
        ),
      );
      return true;
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.video.title),
      ),
      body: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Video player
              Container(
                height: MediaQuery.of(context).size.height * 0.5, // 50% of screen height
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: _buildVideoPlayer(),
                ),
              ),
              // Download for Offline button
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.download),
                  label: const Text('Download for Offline'),
                  onPressed: _downloadAndEncryptVideo,
                ),
              ),

              // Video details
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title
                      Text(
                        widget.video.title,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Video metadata
                      Row(
                        children: [
                          // Week and sequence
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              'Week ${widget.video.weekNumber}',
                              style: TextStyle(
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ),

                          const SizedBox(width: 8),

                          // Duration
                          if (widget.video.durationMinutes != null)
                            Row(
                              children: [
                                Icon(
                                  Icons.access_time,
                                  size: 16,
                                  color: Colors.grey[600],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${widget.video.durationMinutes} min',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),

                          const Spacer(),

                          // Completion status
                          if (_isCompleted)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.green.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: Colors.green),
                              ),
                              child: const Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.check,
                                    size: 14,
                                    color: Colors.green,
                                  ),
                                  SizedBox(width: 4),
                                  Text(
                                    'Completed',
                                    style: TextStyle(
                                      color: Colors.green,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Description
                      if (widget.video.description.isNotEmpty)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Description',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              widget.video.description,
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey[700],
                              ),
                            ),
                            const SizedBox(height: 24),
                          ],
                        ),

                      // Mark as completed button
                      if (!_isCompleted)
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: _isLoading ? null : _markAsCompleted,
                            icon: _isLoading
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                    ),
                                  )
                                : const Icon(Icons.check),
                            label: const Text('Mark as Completed'),
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),

                      // Error message is now displayed in the video player area
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
