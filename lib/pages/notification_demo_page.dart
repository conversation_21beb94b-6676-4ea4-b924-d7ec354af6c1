import 'package:flutter/material.dart';
import '../services/awesome_notification_service.dart';

class NotificationDemoPage extends StatefulWidget {
  const NotificationDemoPage({Key? key}) : super(key: key);

  @override
  State<NotificationDemoPage> createState() => _NotificationDemoPageState();
}

class _NotificationDemoPageState extends State<NotificationDemoPage> {
  final EnhancedNotificationService _notificationService = EnhancedNotificationService();
  Map<String, dynamic>? _debugInfo;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadDebugInfo();
  }

  Future<void> _loadDebugInfo() async {
    setState(() => _isLoading = false);
    // Debug info not available
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Demo'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDebugInfo,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildWelcomeCard(theme),
                  const SizedBox(height: 16),
                  _buildQuickTestSection(theme),
                  const SizedBox(height: 16),
                  _buildWaterReminderSection(theme),
                  const SizedBox(height: 16),
                  _buildWorkoutReminderSection(theme),
                  const SizedBox(height: 16),
                  _buildCustomWorkoutSection(theme),
                  const SizedBox(height: 16),
                  _buildDebugSection(theme),
                  const SizedBox(height: 16),
                  _buildNavigationSection(theme),
                ],
              ),
            ),
    );
  }

  Widget _buildWelcomeCard(ThemeData theme) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              theme.colorScheme.primary,
              theme.colorScheme.primary.withOpacity(0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.notifications_active,
                  color: Colors.white,
                  size: 32,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Awesome Notifications Demo',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Test and configure your enhanced notification system with water reminders, workout alerts, and custom scheduling.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.white.withOpacity(0.9),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickTestSection(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.flash_on, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Quick Tests',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _testNotification('water'),
                  icon: const Icon(Icons.water_drop),
                  label: const Text('Test Water'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF06B6D4),
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _testNotification('workout'),
                  icon: const Icon(Icons.fitness_center),
                  label: const Text('Test Workout'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6366F1),
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _testNotification('general'),
                  icon: const Icon(Icons.notifications),
                  label: const Text('Test General'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4F46E5),
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _testNotification('16second'),
                  icon: const Icon(Icons.timer),
                  label: const Text('16s Test'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFF8C00),
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _testNotification('17second'),
                  icon: const Icon(Icons.timer_10),
                  label: const Text('17s Test'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFF5722),
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWaterReminderSection(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.water_drop, color: const Color(0xFF06B6D4)),
                const SizedBox(width: 8),
                Text(
                  'Water Reminders',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Enhanced water reminders with customizable intervals, snooze functionality, and quick action buttons.',
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _scheduleWaterReminders(),
                    icon: const Icon(Icons.schedule),
                    label: const Text('Schedule Water Reminders'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _cancelWaterReminders(),
                    icon: const Icon(Icons.cancel),
                    label: const Text('Cancel Water Reminders'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkoutReminderSection(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.fitness_center, color: const Color(0xFF6366F1)),
                const SizedBox(width: 8),
                Text(
                  'Workout Reminders',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Daily workout reminders with full-screen alerts, action buttons, and customizable timing.',
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _scheduleWorkoutReminder(),
                    icon: const Icon(Icons.schedule),
                    label: const Text('Schedule Workout'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _cancelWorkoutReminder(),
                    icon: const Icon(Icons.cancel),
                    label: const Text('Cancel Workout'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomWorkoutSection(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.schedule_outlined, color: const Color(0xFF8B5CF6)),
                const SizedBox(width: 8),
                Text(
                  'Custom Workout Times',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Set multiple custom workout reminders for different times, workout types, and specific days of the week.',
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _addSampleCustomWorkouts(),
                    icon: const Icon(Icons.add),
                    label: const Text('Add Sample Workouts'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _clearCustomWorkouts(),
                    icon: const Icon(Icons.clear),
                    label: const Text('Clear Custom'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDebugSection(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.bug_report, color: const Color(0xFFFF6B6B)),
                const SizedBox(width: 8),
                Text(
                  'Debug Information',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text('Debug info is not available in this build.'),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationSection(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Settings & Configuration',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => Navigator.pushNamed(context, '/enhanced-notification-settings'),
                icon: const Icon(Icons.tune),
                label: const Text('Open Enhanced Notification Settings'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => Navigator.pushNamed(context, '/notification-settings'),
                icon: const Icon(Icons.settings_outlined),
                label: const Text('Open Legacy Notification Settings'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Action methods
  Future<void> _testNotification(String type) async {
    try {
      switch (type) {
        case 'water':
        case 'workout':
        case 'general':
          await _notificationService.send16SecondTest();
          _showSnackBar('16-second test notification scheduled! 🎉', Colors.orange);
          break;
        case '16second':
          await _notificationService.send16SecondTest();
          _showSnackBar('16-second test notification scheduled! 🎉', Colors.orange);
          break;
        case '17second':
          await _notificationService.send17SecondTest();
          _showSnackBar('17-second test notification scheduled! 🚀', Colors.deepOrange);
          break;
      }
    } catch (e) {
      _showSnackBar('Failed to send test notification: $e', Colors.red);
    }
  }

  Future<void> _scheduleWaterReminders() async {
    try {
      await _notificationService.scheduleWaterReminders(dailyTarget: 2000);
      _showSnackBar('Water reminders scheduled!', Colors.blue);
      _loadDebugInfo();
    } catch (e) {
      _showSnackBar('Failed to schedule water reminders: $e', Colors.red);
    }
  }

  Future<void> _cancelWaterReminders() async {
    try {
      await _notificationService.cancelWaterReminders();
      _showSnackBar('Water reminders cancelled!', Colors.orange);
      _loadDebugInfo();
    } catch (e) {
      _showSnackBar('Failed to cancel water reminders: $e', Colors.red);
    }
  }

  Future<void> _scheduleWorkoutReminder() async {
    try {
      await _notificationService.scheduleWorkoutReminder(TimeOfDay(hour: 18, minute: 0));
      _showSnackBar('Workout reminder scheduled!', Colors.purple);
      _loadDebugInfo();
    } catch (e) {
      _showSnackBar('Failed to schedule workout reminder: $e', Colors.red);
    }
  }

  Future<void> _cancelWorkoutReminder() async {
    try {
      await _notificationService.cancelWorkoutReminders();
      _showSnackBar('Workout reminder cancelled!', Colors.orange);
      _loadDebugInfo();
    } catch (e) {
      _showSnackBar('Failed to cancel workout reminder: $e', Colors.red);
    }
  }

  Future<void> _addSampleCustomWorkouts() async {
    try {
      final sampleWorkouts = [
        {
          'time': '06:30',
          'type': 'Morning Cardio',
          'days': [1, 2, 3, 4, 5], // Weekdays
          'enabled': true,
        },
        {
          'time': '18:00',
          'type': 'Evening Strength',
          'days': [1, 3, 5], // Mon, Wed, Fri
          'enabled': true,
        },
        {
          'time': '10:00',
          'type': 'Weekend Yoga',
          'days': [6, 7], // Sat, Sun
          'enabled': true,
        },
      ];

      await _notificationService.setCustomWorkoutTimes(sampleWorkouts);
      _showSnackBar('Sample custom workouts added!', Colors.green);
      _loadDebugInfo();
    } catch (e) {
      _showSnackBar('Failed to add sample workouts: $e', Colors.red);
    }
  }

  Future<void> _clearCustomWorkouts() async {
    try {
      await _notificationService.setCustomWorkoutTimes([]);
      _showSnackBar('Custom workouts cleared!', Colors.orange);
      _loadDebugInfo();
    } catch (e) {
      _showSnackBar('Failed to clear custom workouts: $e', Colors.red);
    }
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
