import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/awesome_notification_service.dart';

class EnhancedNotificationSettingsPage extends ConsumerStatefulWidget {
  const EnhancedNotificationSettingsPage({Key? key}) : super(key: key);

  @override
  ConsumerState<EnhancedNotificationSettingsPage> createState() => _EnhancedNotificationSettingsPageState();
}

class _EnhancedNotificationSettingsPageState extends ConsumerState<EnhancedNotificationSettingsPage> {
  final EnhancedNotificationService _notificationService = EnhancedNotificationService();

  bool _workoutNotificationsEnabled = true;
  bool _waterNotificationsEnabled = true;
  String _workoutTime = '07:00';
  int _waterInterval = 2;
  bool _hasPermissions = false;
  bool _isLoading = true;
  bool _workoutReminderScheduled = false;
  bool _customWorkoutNotificationsScheduled = false;
  List<Map<String, dynamic>> _customWorkoutTimes = [];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);
    
    try {
      final hasPerms = await _notificationService.hasPermissions();
      final workoutEnabled = await _notificationService.isWorkoutNotificationEnabled();
      final waterEnabled = await _notificationService.isWaterNotificationEnabled();
      final workoutTime = await _notificationService.getWorkoutReminderTime();
      final waterInterval = await _notificationService.getWaterReminderInterval();
      final customTimes = await _notificationService.getCustomWorkoutTimes();
      final workoutScheduled = await _notificationService.isWorkoutReminderScheduled();
      final customWorkoutScheduled = await _notificationService.areCustomWorkoutNotificationsScheduled();

      if (mounted) {
        setState(() {
          _hasPermissions = hasPerms;
          _workoutNotificationsEnabled = workoutEnabled;
          _waterNotificationsEnabled = waterEnabled;
          _workoutTime = workoutTime != null ? '${workoutTime.hour.toString().padLeft(2, '0')}:${workoutTime.minute.toString().padLeft(2, '0')}' : '07:00';
          _waterInterval = waterInterval;
          _customWorkoutTimes = customTimes;
          _workoutReminderScheduled = workoutScheduled;
          _customWorkoutNotificationsScheduled = customWorkoutScheduled;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('❌ Error loading settings: $e');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Enhanced Notifications'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPermissionSection(theme),
                  const SizedBox(height: 24),
                  _buildWaterReminderSection(theme),
                  const SizedBox(height: 24),
                  _buildWorkoutReminderSection(theme),
                  const SizedBox(height: 24),
                  _buildCustomWorkoutSection(theme),
                  const SizedBox(height: 24),
                  _buildDebugInfoSection(theme),
                ],
              ),
            ),
    );
  }

  Widget _buildPermissionSection(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _hasPermissions ? Icons.check_circle : Icons.warning,
                  color: _hasPermissions ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  'Notification Permissions',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _hasPermissions 
                  ? 'Notifications are enabled and working properly.'
                  : 'Notification permissions are required for reminders to work.',
              style: theme.textTheme.bodyMedium,
            ),
            if (!_hasPermissions) ...[
              const SizedBox(height: 12),
              ElevatedButton(
                onPressed: _requestPermissions,
                child: const Text('Grant Permissions'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildWaterReminderSection(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.water_drop, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Water Reminders',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Switch(
                  value: _waterNotificationsEnabled,
                  onChanged: _hasPermissions ? _toggleWaterNotifications : null,
                ),
              ],
            ),
            if (_waterNotificationsEnabled) ...[
              const SizedBox(height: 16),
              Text(
                'Reminder Interval',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: [1, 2, 3, 4].map((hours) {
                  final isSelected = _waterInterval == hours;
                  return FilterChip(
                    label: Text('${hours}h'),
                    selected: isSelected,
                    onSelected: (selected) {
                      if (selected) _setWaterInterval(hours);
                    },
                    selectedColor: theme.colorScheme.primary.withOpacity(0.2),
                  );
                }).toList(),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _test16SecondNotification,
                      icon: const Icon(Icons.timer),
                      label: const Text('16s Test'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _test17SecondNotification,
                      icon: const Icon(Icons.timer_10),
                      label: const Text('17s Test'),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _test21SecondWaterNotification,
                  icon: const Icon(Icons.water_drop),
                  label: const Text('21s Water Test'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildWorkoutReminderSection(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.fitness_center, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Workout Reminders',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Switch(
                  value: _workoutNotificationsEnabled,
                  onChanged: _hasPermissions ? _toggleWorkoutNotifications : null,
                ),
              ],
            ),
            if (_workoutNotificationsEnabled) ...[
              const SizedBox(height: 16),
              ListTile(
                contentPadding: EdgeInsets.zero,
                leading: const Icon(Icons.schedule),
                title: const Text('Workout Time'),
                subtitle: Text(_formatTimeDisplay()),
                trailing: const Icon(Icons.chevron_right),
                onTap: _selectWorkoutTime,
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _workoutReminderScheduled 
                      ? Colors.green.withOpacity(0.1)
                      : Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _workoutReminderScheduled ? Colors.green : Colors.orange,
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _workoutReminderScheduled ? Icons.check_circle : Icons.warning,
                      color: _workoutReminderScheduled ? Colors.green : Colors.orange,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _workoutReminderScheduled 
                            ? 'Workout reminder is scheduled and working! ✅'
                            : 'Workout reminder is not currently scheduled ⚠️',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: _workoutReminderScheduled ? Colors.green : Colors.orange,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: _refreshWorkoutStatus,
                      icon: const Icon(Icons.refresh, size: 18),
                      tooltip: 'Refresh Status',
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _test16SecondNotification,
                      icon: const Icon(Icons.timer),
                      label: const Text('16s Test'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _test17SecondNotification,
                      icon: const Icon(Icons.timer_10),
                      label: const Text('17s Test'),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _testWorkoutReminderToday,
                  icon: const Icon(Icons.fitness_center),
                  label: const Text('Test Today\'s Workout Time'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: theme.colorScheme.primary,
                    side: BorderSide(color: theme.colorScheme.primary),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCustomWorkoutSection(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.schedule_outlined, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Custom Workout Times',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _addCustomWorkoutTime,
                  icon: const Icon(Icons.add),
                  tooltip: 'Add Custom Time',
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Set multiple workout reminders for different times and workout types.',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            // --- Custom Workout Times Section Disabled ---
            // if (_customWorkoutTimes.isNotEmpty) ...[
            //   Container(
            //     padding: const EdgeInsets.all(12),
            //     decoration: BoxDecoration(
            //       color: _customWorkoutNotificationsScheduled 
            //           ? Colors.green.withOpacity(0.1)
            //           : Colors.orange.withOpacity(0.1),
            //       borderRadius: BorderRadius.circular(8),
            //       border: Border.all(
            //         color: _customWorkoutNotificationsScheduled ? Colors.green : Colors.orange,
            //         width: 1,
            //       ),
            //     ),
            //     child: Row(
            //       children: [
            //         Icon(
            //           _customWorkoutNotificationsScheduled ? Icons.check_circle : Icons.warning,
            //         ),
            //         const SizedBox(width: 8),
            //         Expanded(
            //           child: Text(
            //             _customWorkoutNotificationsScheduled 
            //                 ? 'Custom workout notifications are scheduled and working'
            //                 : 'No custom workout notifications scheduled',
            //             style: TextStyle(
            //               color: _customWorkoutNotificationsScheduled ? Colors.green : Colors.orange,
            //               fontWeight: FontWeight.w500,
            //             ),
            //           ),
            //         ),
            //         IconButton(
            //           onPressed: _refreshCustomWorkoutStatus,
            //           icon: const Icon(Icons.refresh),
            //           tooltip: 'Refresh status',
            //         ),
            //       ],
            //     ),
            //   ),
            //   ..._customWorkoutTimes.asMap().entries.map((entry) {
            //     final index = entry.key;
            //     final workoutTime = entry.value;
            //     return _buildCustomWorkoutTimeItem(theme, workoutTime, index);
            //   }).toList(),
            // ],
          ],
        ),
      ),
    );
  }

  Widget _buildCustomWorkoutTimeItem(ThemeData theme, Map<String, dynamic> workoutTime, int index) {
    final time = workoutTime['time'] as String;
    final type = workoutTime['type'] as String? ?? 'General Workout';
    final enabled = workoutTime['enabled'] as bool? ?? true;
    final days = List<int>.from(workoutTime['days'] ?? [1, 2, 3, 4, 5, 6, 7]);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Switch(
                  value: enabled,
                  onChanged: (value) => _toggleCustomWorkoutTime(index, value),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '$type - $time',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _formatDays(days),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton(
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'test',
                      child: const Row(
                        children: [
                          Icon(Icons.science),
                          SizedBox(width: 8),
                          Text('Test Today'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'edit',
                      child: const Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'delete',
                      child: const Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete'),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) {
                    if (value == 'test') {
                      _testCustomWorkoutNotification(index);
                    } else if (value == 'edit') {
                      _editCustomWorkoutTime(index);
                    } else if (value == 'delete') {
                      _deleteCustomWorkoutTime(index);
                    }
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Event handlers and utility methods
  Future<void> _requestPermissions() async {
    final granted = await _notificationService.requestPermissions();
    setState(() => _hasPermissions = granted);

    if (granted) {
      _showSuccessSnackBar('Notification permissions granted!');
    } else {
      _showErrorSnackBar('Notification permissions denied. Some features may not work.');
    }
  }

  Future<void> _toggleWaterNotifications(bool enabled) async {
    try {
      await _notificationService.setWaterNotificationEnabled(enabled);
      setState(() => _waterNotificationsEnabled = enabled);
      _showSuccessSnackBar(enabled ? 'Water reminders enabled' : 'Water reminders disabled');
    } catch (e) {
      _showErrorSnackBar('Failed to update water reminders: $e');
    }
  }

  Future<void> _toggleWorkoutNotifications(bool enabled) async {
    try {
      await _notificationService.setWorkoutNotificationEnabled(enabled);
      final workoutScheduled = await _notificationService.isWorkoutReminderScheduled();
      setState(() {
        _workoutNotificationsEnabled = enabled;
        _workoutReminderScheduled = workoutScheduled;
      });
      _showSuccessSnackBar(enabled ? 'Workout reminders enabled' : 'Workout reminders disabled');
    } catch (e) {
      _showErrorSnackBar('Failed to update workout reminders: $e');
    }
  }

  Future<void> _setWaterInterval(int hours) async {
    try {
      await _notificationService.setWaterReminderInterval(hours);
      setState(() => _waterInterval = hours);
      _showSuccessSnackBar('Water reminder interval set to ${hours}h');
    } catch (e) {
      _showErrorSnackBar('Failed to update water interval: $e');
    }
  }

  Future<void> _selectWorkoutTime() async {
    final currentTime = _parseTimeString(_workoutTime);

    final selectedTime = await showTimePicker(
      context: context,
      initialTime: currentTime,
      helpText: 'Select workout reminder time',
    );

    if (selectedTime != null) {
      final timeString = '${selectedTime.hour.toString().padLeft(2, '0')}:${selectedTime.minute.toString().padLeft(2, '0')}';

      try {
        await _notificationService.setWorkoutReminderTime(_formatTimeOfDay(selectedTime));
        final workoutScheduled = await _notificationService.isWorkoutReminderScheduled();
        setState(() {
          _workoutTime = timeString;
          _workoutReminderScheduled = workoutScheduled;
        });
        _showSuccessSnackBar('Workout time set to ${_formatTimeDisplay()}');
      } catch (e) {
        _showErrorSnackBar('Failed to update workout time: $e');
      }
    }
  }

  String _formatTimeDisplay() {
    final time = _parseTimeString(_workoutTime);
    return time.format(context);
  }

  String _formatDays(List<int> days) {
    const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    if (days.length == 7) return 'Every day';
    if (days.length == 5 && !days.contains(6) && !days.contains(7)) return 'Weekdays';
    if (days.length == 2 && days.contains(6) && days.contains(7)) return 'Weekends';

    return days.map((day) => dayNames[day - 1]).join(', ');
  }

  // Custom workout time methods
  Future<void> _addCustomWorkoutTime() async {
    final result = await _showCustomWorkoutDialog();
    if (result != null) {
      final updatedTimes = List<Map<String, dynamic>>.from(_customWorkoutTimes);
      updatedTimes.add(result);

      try {
        await _notificationService.setCustomWorkoutTimes(updatedTimes);
        final customWorkoutScheduled = await _notificationService.areCustomWorkoutNotificationsScheduled();
        setState(() {
          _customWorkoutTimes = updatedTimes;
          _customWorkoutNotificationsScheduled = customWorkoutScheduled;
        });
        _showSuccessSnackBar('Custom workout time added and scheduled! 🎉');
      } catch (e) {
        _showErrorSnackBar('Failed to add custom workout time: $e');
      }
    }
  }

  Future<void> _editCustomWorkoutTime(int index) async {
    final currentTime = _customWorkoutTimes[index];
    final result = await _showCustomWorkoutDialog(currentTime);

    if (result != null) {
      final updatedTimes = List<Map<String, dynamic>>.from(_customWorkoutTimes);
      updatedTimes[index] = result;

      try {
        await _notificationService.setCustomWorkoutTimes(updatedTimes);
        final customWorkoutScheduled = await _notificationService.areCustomWorkoutNotificationsScheduled();
        setState(() {
          _customWorkoutTimes = updatedTimes;
          _customWorkoutNotificationsScheduled = customWorkoutScheduled;
        });
        _showSuccessSnackBar('Custom workout time updated and scheduled! 🎉');
      } catch (e) {
        _showErrorSnackBar('Failed to update custom workout time: $e');
      }
    }
  }

  Future<void> _deleteCustomWorkoutTime(int index) async {
    final updatedTimes = List<Map<String, dynamic>>.from(_customWorkoutTimes);
    updatedTimes.removeAt(index);

    try {
      await _notificationService.setCustomWorkoutTimes(updatedTimes);
      final customWorkoutScheduled = await _notificationService.areCustomWorkoutNotificationsScheduled();
      setState(() {
        _customWorkoutTimes = updatedTimes;
        _customWorkoutNotificationsScheduled = customWorkoutScheduled;
      });
      _showSuccessSnackBar('Custom workout time deleted');
    } catch (e) {
      _showErrorSnackBar('Failed to delete custom workout time: $e');
    }
  }

  Future<void> _toggleCustomWorkoutTime(int index, bool enabled) async {
    final updatedTimes = List<Map<String, dynamic>>.from(_customWorkoutTimes);
    updatedTimes[index]['enabled'] = enabled;

    try {
      await _notificationService.setCustomWorkoutTimes(updatedTimes);
      final customWorkoutScheduled = await _notificationService.areCustomWorkoutNotificationsScheduled();
      setState(() {
        _customWorkoutTimes = updatedTimes;
        _customWorkoutNotificationsScheduled = customWorkoutScheduled;
      });
      _showSuccessSnackBar(enabled ? 'Custom workout time enabled and scheduled' : 'Custom workout time disabled');
    } catch (e) {
      _showErrorSnackBar('Failed to toggle custom workout time: $e');
    }
  }

  Future<Map<String, dynamic>?> _showCustomWorkoutDialog([Map<String, dynamic>? existingTime]) async {
    final timeController = TextEditingController(text: existingTime?['time'] ?? '07:00');
    final typeController = TextEditingController(text: existingTime?['type'] ?? '');
    final selectedDays = Set<int>.from(existingTime?['days'] ?? [1, 2, 3, 4, 5, 6, 7]);
    bool enabled = existingTime?['enabled'] ?? true;

    return showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(existingTime == null ? 'Add Custom Workout Time' : 'Edit Custom Workout Time'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: typeController,
                  decoration: const InputDecoration(
                    labelText: 'Workout Type',
                    hintText: 'e.g., Cardio, Strength, Yoga',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                InkWell(
                  onTap: () async {
                    final timeParts = timeController.text.split(':');
                    final currentTime = TimeOfDay(
                      hour: int.parse(timeParts[0]),
                      minute: int.parse(timeParts[1]),
                    );

                    final selectedTime = await showTimePicker(
                      context: context,
                      initialTime: currentTime,
                    );

                    if (selectedTime != null) {
                      timeController.text = '${selectedTime.hour.toString().padLeft(2, '0')}:${selectedTime.minute.toString().padLeft(2, '0')}';
                    }
                  },
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'Time',
                      border: OutlineInputBorder(),
                    ),
                    child: Text(timeController.text),
                  ),
                ),
                const SizedBox(height: 16),
                const Text('Days of Week:'),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: [
                    for (int day = 1; day <= 7; day++)
                      FilterChip(
                        label: Text(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][day - 1]),
                        selected: selectedDays.contains(day),
                        onSelected: (selected) {
                          setDialogState(() {
                            if (selected) {
                              selectedDays.add(day);
                            } else {
                              selectedDays.remove(day);
                            }
                          });
                        },
                      ),
                  ],
                ),
                const SizedBox(height: 16),
                SwitchListTile(
                  title: const Text('Enabled'),
                  value: enabled,
                  onChanged: (value) {
                    setDialogState(() => enabled = value);
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (typeController.text.isNotEmpty && selectedDays.isNotEmpty) {
                  Navigator.pop(context, {
                    'time': timeController.text,
                    'type': typeController.text,
                    'days': selectedDays.toList(),
                    'enabled': enabled,
                  });
                }
              },
              child: Text(existingTime == null ? 'Add' : 'Update'),
            ),
          ],
        ),
      ),
    );
  }

  // Testing methods
  Future<void> _test16SecondNotification() async {
    try {
      await _notificationService.send16SecondTest();
      _showSuccessSnackBar('16-second test notification scheduled! 🎉');
    } catch (e) {
      _showErrorSnackBar('Failed to send 16-second test: $e');
    }
  }

  Future<void> _test17SecondNotification() async {
    try {
      await _notificationService.send17SecondTest();
      _showSuccessSnackBar('17-second test notification scheduled! 🚀');
    } catch (e) {
      _showErrorSnackBar('Failed to send 17-second test: $e');
    }
  }

  Future<void> _test21SecondWaterNotification() async {
    try {
      await _notificationService.send21SecondTest();
      _showSuccessSnackBar('21-second water test notification scheduled! 💧');
    } catch (e) {
      _showErrorSnackBar('Failed to send 21-second water test: $e');
    }
  }

  Future<void> _testWorkoutReminderToday() async {
    try {
      final workoutTime = _parseTimeString(_workoutTime);
      
      await _notificationService.testWorkoutReminderToday(workoutTime);
      final workoutScheduled = await _notificationService.isWorkoutReminderScheduled();
      setState(() => _workoutReminderScheduled = workoutScheduled);
      _showSuccessSnackBar('Today\'s workout reminder scheduled! 🎉');
    } catch (e) {
      _showErrorSnackBar('Failed to send today\'s workout reminder: $e');
    }
  }

  Future<void> _testCustomWorkoutNotification(int index) async {
    try {
      final workoutTime = _customWorkoutTimes[index];
      await _notificationService.testCustomWorkoutNotificationToday(workoutTime);
      _showSuccessSnackBar('Custom workout test notification scheduled! 🧪');
    } catch (e) {
      _showErrorSnackBar('Failed to send custom workout test notification: $e');
    }
  }

  // Utility methods
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  String _formatTimeOfDay(TimeOfDay time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  /// Helper method to parse time string to TimeOfDay
  TimeOfDay _parseTimeString(String timeString) {
    // Handle different time formats
    if (timeString.contains('AM') || timeString.contains('PM')) {
      // 12-hour format like "8:58 AM" or "2:30 PM"
      final parts = timeString.split(' ');
      final timePart = parts[0];
      final period = parts[1];
      
      final timeComponents = timePart.split(':');
      int hour = int.parse(timeComponents[0]);
      int minute = int.parse(timeComponents[1]);
      
      // Convert to 24-hour format
      if (period == 'PM' && hour != 12) {
        hour += 12;
      } else if (period == 'AM' && hour == 12) {
        hour = 0;
      }
      
      return TimeOfDay(hour: hour, minute: minute);
    } else {
      // 24-hour format like "08:58"
      final timeParts = timeString.split(':');
      return TimeOfDay(
        hour: int.parse(timeParts[0]),
        minute: int.parse(timeParts[1]),
      );
    }
  }

  Future<void> _refreshWorkoutStatus() async {
    try {
      final workoutScheduled = await _notificationService.isWorkoutReminderScheduled();
      setState(() => _workoutReminderScheduled = workoutScheduled);
      _showSuccessSnackBar(workoutScheduled ? 'Workout reminder is scheduled and working! ✅' : 'Workout reminder is not currently scheduled ⚠️');
    } catch (e) {
      _showErrorSnackBar('Failed to refresh workout status: $e');
    }
  }

  Future<void> _refreshCustomWorkoutStatus() async {
    try {
      final customWorkoutScheduled = await _notificationService.areCustomWorkoutNotificationsScheduled();
      setState(() => _customWorkoutNotificationsScheduled = customWorkoutScheduled);
      _showSuccessSnackBar(customWorkoutScheduled ? 'Custom workout notifications are scheduled and working' : 'No custom workout notifications scheduled');
    } catch (e) {
      _showErrorSnackBar('Failed to refresh custom workout status: $e');
    }
  }

  Widget _buildDebugInfoSection(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.bug_report, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Debug Information',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _loadDebugInfo,
                  icon: const Icon(Icons.refresh),
                  tooltip: 'Refresh Debug Info',
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'System status and scheduled notifications information.',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 16),
            _buildDebugInfoContent(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildDebugInfoContent(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDebugItem('Permissions Granted', _hasPermissions ? 'Yes' : 'No', _hasPermissions ? Colors.green : Colors.red),
        _buildDebugItem('Workout Notifications', _workoutNotificationsEnabled ? 'Enabled' : 'Disabled', _workoutNotificationsEnabled ? Colors.green : Colors.orange),
        _buildDebugItem('Workout Reminder Scheduled', _workoutReminderScheduled ? 'Yes' : 'No', _workoutReminderScheduled ? Colors.green : Colors.red),
        _buildDebugItem('Workout Time', _workoutTime, theme.colorScheme.primary),
        _buildDebugItem('Water Notifications', _waterNotificationsEnabled ? 'Enabled' : 'Disabled', _waterNotificationsEnabled ? Colors.green : Colors.orange),
        _buildDebugItem('Water Interval', '${_waterInterval}h', theme.colorScheme.primary),
        _buildDebugItem('Custom Workout Times', '${_customWorkoutTimes.length} configured', theme.colorScheme.primary),
      ],
    );
  }

  Widget _buildDebugItem(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              value,
              style: TextStyle(fontSize: 12, color: color, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _loadDebugInfo() async {
    try {
      final hasPerms = await _notificationService.hasPermissions();
      final workoutEnabled = await _notificationService.isWorkoutNotificationEnabled();
      final workoutScheduled = await _notificationService.isWorkoutReminderScheduled();
      final scheduledNotifications = await _notificationService.getScheduledNotificationsInfo();
      final workoutSummary = await _notificationService.getWorkoutReminderSummary();
      
      setState(() {
        _hasPermissions = hasPerms;
        _workoutNotificationsEnabled = workoutEnabled;
        _workoutReminderScheduled = workoutScheduled;
      });
      
      print('🔍 Debug Info:');
      print('  - Permissions: $_hasPermissions');
      print('  - Workout Enabled: $_workoutNotificationsEnabled');
      print('  - Workout Scheduled: $_workoutReminderScheduled');
      print('  - Scheduled Notifications: ${scheduledNotifications.length}');
      print('  - Workout Summary: $workoutSummary');
      for (final notification in scheduledNotifications) {
        print('    - ID: ${notification['id']}, Title: ${notification['title']}, Scheduled: ${notification['scheduledDate']}');
      }
      
      _showSuccessSnackBar('Debug info refreshed! Check console for details.');
    } catch (e) {
      _showErrorSnackBar('Failed to load debug info: $e');
    }
  }
}
