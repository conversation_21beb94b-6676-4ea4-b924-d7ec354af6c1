import 'package:flutter/material.dart';
import '../widgets/notification_panel.dart';

class NotificationPage extends StatelessWidget {
  const NotificationPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
      ),
      body: NotificationPanel(
        onClose: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }
} 